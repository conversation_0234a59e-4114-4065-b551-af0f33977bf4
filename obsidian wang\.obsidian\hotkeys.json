{"local-gpt:quick-access-1": [{"modifiers": ["Alt", "Mod"], "key": "1"}], "local-gpt:quick-access-2": [{"modifiers": ["Alt", "Mod"], "key": "2"}], "local-gpt:quick-access-3": [{"modifiers": ["Alt", "Mod"], "key": "3"}], "local-gpt:quick-access-4": [{"modifiers": ["Alt", "Mod"], "key": "4"}], "local-gpt:quick-access-5": [{"modifiers": ["Alt", "Mod"], "key": "5"}], "local-gpt:quick-access-6": [{"modifiers": ["Alt", "Mod"], "key": "6"}], "local-gpt:context-menu": [{"modifiers": ["Alt", "Mod"], "key": "7"}], "chatgpt-md:call-chatgpt-api": [{"modifiers": ["Alt", "Mod"], "key": "C"}], "daily-notes": [{"modifiers": ["Alt", "Mod"], "key": "O"}], "editor:set-heading": [{"modifiers": ["Alt", "Mod"], "key": "X"}], "editor:attach-file": [{"modifiers": ["Alt", "Mod"], "key": "D"}], "editor:insert-table": [{"modifiers": ["Alt", "Mod"], "key": "I"}], "editor:insert-callout": [{"modifiers": ["Alt", "Mod"], "key": "/"}], "editor:cycle-list-checklist": [{"modifiers": ["Alt", "Mod"], "key": "L"}], "editor:toggle-highlight": [{"modifiers": ["Alt", "Mod"], "key": "."}], "workspace:open-in-new-window": [{"modifiers": ["Alt", "Mod", "Shift"], "key": "O"}], "copilot:translate%20to%20chinese": [{"modifiers": ["Alt", "Mod"], "key": "T"}]}