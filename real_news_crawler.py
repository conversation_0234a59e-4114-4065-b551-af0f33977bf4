#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
真实菲律宾新闻爬虫
使用RSS源和API获取真实新闻数据
"""

import os
import time
import datetime
import logging
import re
import requests
from pathlib import Path
import feedparser

class RealNewsAnalyzer:
    def __init__(self):
        """初始化真实新闻分析器"""
        self.setup_logging()

        # 翻译API配置
        self.translation_config = {
            "api_key": "7810b54663644292bcfd221e9ecf7f0dd.aPV0rs8StKq1FmD0",
            "base_url": "https://open.bigmodel.cn/api/paas/v4",
            "model": "GLM-4-Flash"
        }

        # 输出目录
        self.output_dir = r"E:\mcp-test\obsidian wang\newspaper"
        Path(self.output_dir).mkdir(parents=True, exist_ok=True)

        # 菲律宾新闻RSS源
        self.news_sources = {
            "Inquirer.net": {
                "rss": "https://www.inquirer.net/fullfeed",
                "website": "https://www.inquirer.net/",
                "weight": 1.2  # 权威性权重
            },
            "PhilStar": {
                "rss": "https://www.philstar.com/rss/headlines",
                "website": "https://www.philstar.com/",
                "weight": 1.1
            },
            "GMA News": {
                "rss": "https://www.gmanetwork.com/news/rss/",
                "website": "https://www.gmanetwork.com/news/",
                "weight": 1.1
            },
            "Rappler": {
                "rss": "https://www.rappler.com/nation/feed/",
                "website": "https://www.rappler.com/",
                "weight": 1.0
            },
            "Manila Bulletin": {
                "rss": "https://mb.com.ph/feed/",
                "website": "https://mb.com.ph/",
                "weight": 1.0
            }
        }

    def setup_logging(self):
        """设置日志"""
        logging.basicConfig(
            level=logging.INFO,
            format="%(asctime)s - %(levelname)s - %(message)s",
            handlers=[
                logging.FileHandler("real_news_crawler.log", encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)

    def fetch_rss_news(self, source_name, rss_url, max_items=20):
        """从RSS源获取新闻"""
        try:
            self.logger.info(f"获取RSS新闻: {source_name}")

            # 使用feedparser解析RSS
            feed = feedparser.parse(rss_url)

            if feed.bozo:
                self.logger.warning(f"RSS解析警告: {source_name}")

            news_items = []

            for entry in feed.entries[:max_items]:
                try:
                    # 提取新闻信息
                    title = entry.get('title', '').strip()
                    link = entry.get('link', '').strip()
                    summary = entry.get('summary', '').strip()
                    published = entry.get('published', '')

                    # 清理HTML标签
                    if summary:
                        summary = re.sub(r'<[^>]+>', '', summary)
                        summary = re.sub(r'\s+', ' ', summary).strip()

                    if title and link:
                        news_item = {
                            'title': title,
                            'link': link,
                            'summary': summary[:300] if summary else '',  # 限制摘要长度
                            'published': published,
                            'source': source_name,
                            'source_url': self.news_sources[source_name]['website']
                        }

                        # 计算热度分数
                        news_item['popularity_score'] = self.calculate_popularity_score(news_item, source_name)

                        news_items.append(news_item)

                except Exception as e:
                    self.logger.warning(f"处理RSS条目时出错: {str(e)}")
                    continue

            self.logger.info(f"从 {source_name} 获取了 {len(news_items)} 条新闻")
            return news_items

        except Exception as e:
            self.logger.error(f"获取RSS失败 {source_name}: {str(e)}")
            return []

    def calculate_popularity_score(self, news_item, source_name):
        """计算新闻热度分数"""
        score = 10  # 基础分数

        title = news_item['title'].lower()
        summary = news_item.get('summary', '').lower()
        content = f"{title} {summary}"

        # 来源权威性加分
        source_weight = self.news_sources.get(source_name, {}).get('weight', 1.0)
        score *= source_weight

        # 关键词重要性评分
        high_priority_keywords = {
            'breaking': 25, 'urgent': 20, 'exclusive': 20,
            'president': 15, 'marcos': 15, 'duterte': 15,
            'government': 12, 'senate': 10, 'congress': 10,
            'earthquake': 20, 'typhoon': 20, 'disaster': 15,
            'killed': 15, 'died': 15, 'death': 12,
            'arrested': 12, 'corruption': 12, 'scandal': 10,
            'covid': 10, 'pandemic': 8, 'vaccine': 6,
            'economy': 8, 'peso': 6, 'inflation': 8,
            'china': 10, 'taiwan': 8, 'south china sea': 12,
            'election': 12, 'vote': 8, 'campaign': 6
        }

        medium_priority_keywords = {
            'manila': 5, 'cebu': 4, 'davao': 4, 'quezon': 4,
            'police': 6, 'military': 6, 'court': 5,
            'business': 4, 'investment': 4, 'trade': 4,
            'health': 4, 'education': 3, 'transport': 3,
            'fire': 6, 'accident': 6, 'crash': 6
        }

        # 计算关键词分数
        for keyword, points in high_priority_keywords.items():
            if keyword in content:
                score += points

        for keyword, points in medium_priority_keywords.items():
            if keyword in content:
                score += points

        # 时间新鲜度加分
        try:
            if news_item.get('published'):
                # 简单的时间判断
                now = datetime.datetime.now()
                today = now.strftime('%Y-%m-%d')
                if today in news_item['published']:
                    score += 15  # 今天的新闻加分
        except:
            pass

        # 标题长度适中加分
        title_len = len(news_item['title'])
        if 30 <= title_len <= 120:
            score += 5

        return round(score, 1)

    def translate_text(self, text, target_language="中文"):
        """使用GLM-4-Air模型翻译文本"""
        if not text or len(text.strip()) < 5:
            return text

        try:
            headers = {
                "Authorization": f"Bearer {self.translation_config['api_key']}",
                "Content-Type": "application/json"
            }

            # 使用GLM-4-Air模型
            payload = {
                "model": "GLM-4-Air",
                "messages": [
                    {
                        "role": "user",
                        "content": f"请将以下英文新闻翻译成中文，保持新闻的准确性和可读性：\n\n{text}"
                    }
                ],
                "temperature": 0.3,
                "max_tokens": 2000
            }

            response = requests.post(
                f"{self.translation_config['base_url']}/chat/completions",
                headers=headers,
                json=payload,
                timeout=30
            )

            if response.status_code == 200:
                result = response.json()
                translated_text = result["choices"][0]["message"]["content"]
                return translated_text.strip()
            else:
                self.logger.error(f"翻译API请求失败: {response.status_code} - {response.text}")
                return text

        except Exception as e:
            self.logger.error(f"翻译过程中出错: {str(e)}")
            return text

    def crawl_all_real_news(self):
        """爬取所有真实新闻源"""
        self.logger.info("开始爬取真实新闻...")

        all_news = []

        for source_name, source_info in self.news_sources.items():
            rss_url = source_info['rss']
            news_items = self.fetch_rss_news(source_name, rss_url)
            all_news.extend(news_items)

            # 添加延迟避免过快请求
            time.sleep(2)

        if not all_news:
            self.logger.error("未能获取任何新闻")
            return []

        # 按热度排序并选择前10条
        sorted_news = sorted(all_news, key=lambda x: x['popularity_score'], reverse=True)
        top_news = sorted_news[:10]

        self.logger.info(f"总共获取 {len(all_news)} 条新闻，选择热度最高的 {len(top_news)} 条")

        # 翻译新闻
        translated_news = []
        for i, news in enumerate(top_news, 1):
            self.logger.info(f"翻译第 {i} 条新闻: {news['title'][:50]}...")

            # 翻译标题和摘要
            news['title_zh'] = self.translate_text(news['title'])
            if news.get('summary'):
                news['summary_zh'] = self.translate_text(news['summary'])

            translated_news.append(news)
            time.sleep(1)  # 避免API限制

        # 生成汇总文件
        self.generate_summary_file(translated_news)

        return translated_news

    def generate_summary_file(self, news_list):
        """生成汇总文件"""
        timestamp = datetime.datetime.now().strftime("%Y-%m-%d_%H-%M")
        date_str = datetime.datetime.now().strftime("%Y年%m月%d日")
        filename = f"{timestamp}_汇总.md"
        filepath = os.path.join(self.output_dir, filename)

        try:
            with open(filepath, "w", encoding="utf-8") as f:
                # 写入YAML前置元数据
                f.write("---\n")
                f.write(f"title: \"菲律宾新闻热点汇总 - {date_str}\"\n")
                f.write(f"date: {timestamp.replace('_', ' ')}\n")
                f.write(f"type: summary\n")
                f.write(f"news_count: {len(news_list)}\n")
                f.write("tags: [news, philippines, summary, translated, real]\n")
                f.write("---\n\n")

                # 写入汇总标题
                f.write(f"# 菲律宾新闻热点汇总 - {date_str}\n\n")
                f.write(f"**生成时间**: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"**新闻数量**: {len(news_list)} 条\n")
                f.write(f"**数据来源**: RSS真实新闻源\n")
                f.write(f"**排序方式**: 按热度排序（关键词、来源权威性、时效性等综合评分）\n\n")

                f.write("---\n\n")

                # 写入新闻列表
                for i, news in enumerate(news_list, 1):
                    f.write(f"## {i}. {news.get('title_zh', news['title'])}\n\n")

                    # 基本信息
                    f.write(f"**原标题**: {news['title']}\n")
                    f.write(f"**来源**: {news['source']}\n")
                    f.write(f"**热度评分**: {news['popularity_score']:.1f}\n")
                    if news.get('published'):
                        f.write(f"**发布时间**: {news['published']}\n")

                    f.write("\n")

                    # 摘要
                    if news.get('summary_zh'):
                        f.write(f"**摘要**: {news['summary_zh']}\n\n")
                    elif news.get('summary'):
                        f.write(f"**摘要**: {news['summary']}\n\n")

                    # 原文链接
                    f.write(f"**原文链接**: [{news['title']}]({news['link']})\n\n")

                    f.write("---\n\n")

                # 写入统计信息
                f.write("## 统计信息\n\n")

                # 来源分布
                sources = {}
                for news in news_list:
                    source = news['source']
                    sources[source] = sources.get(source, 0) + 1

                f.write("**新闻来源分布**:\n")
                for source, count in sources.items():
                    f.write(f"- {source}: {count} 条\n")

                f.write(f"\n**平均热度评分**: {sum(news['popularity_score'] for news in news_list) / len(news_list):.1f}\n")

            self.logger.info(f"汇总文件已生成: {filepath}")

        except Exception as e:
            self.logger.error(f"生成汇总文件时出错: {str(e)}")


def main():
    """主函数"""
    analyzer = RealNewsAnalyzer()

    print("🚀 真实菲律宾新闻分析器")
    print("📡 从RSS源获取真实新闻数据")
    print("⏱️  预计需要 3-5 分钟完成分析和翻译\n")

    try:
        translated_news = analyzer.crawl_all_real_news()

        if translated_news:
            print(f"\n✅ 分析完成！")
            print(f"📈 共处理了 {len(translated_news)} 条真实热点新闻")
            print(f"📁 汇总文件已保存到: {analyzer.output_dir}")
            print(f"📝 文件名格式: YYYY-MM-DD_HH-MM_汇总.md")

            # 显示前3条新闻标题
            print(f"\n🔥 热度最高的前3条新闻:")
            for i, news in enumerate(translated_news[:3], 1):
                title_zh = news.get('title_zh', news['title'])
                score = news['popularity_score']
                source = news['source']
                print(f"  {i}. {title_zh} (热度: {score:.1f}, 来源: {source})")
        else:
            print("❌ 未能获取到新闻数据，请检查网络连接和RSS源")

    except Exception as e:
        analyzer.logger.error(f"分析过程中出错: {str(e)}")
        print(f"❌ 分析失败: {str(e)}")


if __name__ == "__main__":
    main()
