# 菲律宾新闻智能分析器 - 最终使用指南

## 🎉 恭喜！您的智能新闻分析系统已完成！

### 🌟 核心功能

✅ **智能新闻筛选** - 自动从菲律宾主流新闻网站提取热点新闻  
✅ **热度评分系统** - 基于浏览量、评论数、关键词等综合评分  
✅ **自动翻译** - 使用GLM-4-Flash模型翻译成中文  
✅ **汇总报告** - 生成格式化的Markdown汇总文件  
✅ **定时任务** - 支持每天早上8点自动运行  
✅ **多种运行模式** - 命令行、图形界面、演示模式

### 📁 文件结构

```
E:\mcp-test\
├── enhanced_news_crawler.py          # 增强版基础爬虫
├── news_analyzer_crawler.py          # 智能分析爬虫 (主程序)
├── demo_news_analyzer.py             # 功能演示版
├── philippines_news_crawler.py       # 原始基础爬虫
├── news_config.json                  # 配置文件
├── run_news_analyzer.bat             # Windows图形界面
├── run_news_crawler.bat              # 基础爬虫界面
├── README.md                         # 详细文档
└── obsidian wang\newspaper\          # 输出目录
    ├── YYYY-MM-DD_HH-MM_汇总.md      # 智能汇总文件
    └── YYYY-MM-DD_HH-MM_网站名.md    # 基础爬取文件
```

### 🚀 快速开始

#### 方法1：图形界面（推荐）
双击运行 `run_news_analyzer.bat`，选择相应功能：

1. **🔥 智能分析热点新闻** - 完整的智能分析流程
2. **📰 基础爬取所有新闻网站** - 传统爬取模式
3. **🧪 测试单个网站分析** - 测试特定网站
4. **🌐 功能演示** - 使用模拟数据演示功能
5. **⚙️ 检查crawl4ai服务状态** - 服务状态检查
6. **🚀 启动crawl4ai服务** - 启动爬虫服务
7. **📁 打开新闻文件夹** - 查看输出文件
8. **📋 查看使用说明** - 详细帮助

#### 方法2：命令行使用

```bash
# 智能分析模式（推荐）
python news_analyzer_crawler.py --mode analyze

# 测试单个网站
python news_analyzer_crawler.py --mode test --url "https://www.inquirer.net/"

# 功能演示
python demo_news_analyzer.py

# 基础爬取
python enhanced_news_crawler.py --mode once

# 定时任务
python enhanced_news_crawler.py --mode schedule
```

### 📊 智能分析功能详解

#### 热度评分算法
- **浏览量权重** (最高50分): 浏览量/1000
- **评论数权重** (最高30分): 评论数×0.5
- **关键词加分** (5-20分): 
  - "breaking", "exclusive" (+20分)
  - "president", "marcos" (+10分)
  - "earthquake", "typhoon" (+12分)
  - "arrested", "corruption" (+8分)
- **分类加分** (4-15分):
  - Breaking News (+15分)
  - Politics (+10分)
  - Crime (+8分)
  - Weather (+8分)

#### 新闻筛选流程
1. **内容提取** - 从各大新闻网站提取文章
2. **智能过滤** - 排除广告、导航等无关内容
3. **热度计算** - 基于多维度指标计算热度分数
4. **排序筛选** - 选择热度最高的10条新闻
5. **自动翻译** - 翻译标题和摘要
6. **生成汇总** - 创建格式化的汇总报告

### 📝 输出文件格式

#### 智能汇总文件 (`YYYY-MM-DD_HH-MM_汇总.md`)
```markdown
---
title: "菲律宾新闻热点汇总 - 2025年05月30日"
date: 2025-05-30 07-45
type: summary
news_count: 10
tags: [news, philippines, summary, translated]
---

# 菲律宾新闻热点汇总 - 2025年05月30日

## 1. 突发：棉兰老岛发生重大地震，无海啸威胁

**原标题**: Breaking: Major earthquake hits Mindanao, no tsunami threat
**来源**: https://www.gmanetwork.com/
**热度评分**: 108.2
**浏览量**: 31,200
**评论数**: 203

**摘要**: 菲律宾南部发生6.2级地震，但当局表示未发布海啸预警。

**原文链接**: [Breaking: Major earthquake hits Mindanao...](...)

---
```

### 🕐 定时任务设置

#### 自动定时（推荐）
```bash
# 启动定时任务，每天早上8点自动运行
python enhanced_news_crawler.py --mode schedule
```

#### Windows任务计划程序
1. 打开"任务计划程序"
2. 创建基本任务
3. 设置每天早上8点运行
4. 操作：`python enhanced_news_crawler.py --mode once`

### ⚙️ 配置文件说明

编辑 `news_config.json` 可以自定义：

```json
{
  "schedule": {
    "daily_time": "08:00"          // 修改定时运行时间
  },
  "philippines_news_sites": [      // 添加/删除新闻网站
    {
      "name": "网站名称",
      "url": "https://example.com/",
      "enabled": true              // 启用/禁用网站
    }
  ],
  "crawl_settings": {
    "delay_between_requests": 5,   // 请求间隔（秒）
    "word_count_threshold": 50     // 最小字数阈值
  }
}
```

### 🔧 故障排除

#### crawl4ai服务问题
```bash
# 检查服务状态
docker ps | findstr crawl4ai

# 启动服务
docker start crawl4ai

# 查看日志
docker logs crawl4ai
```

#### 翻译API问题
- 检查API密钥是否正确
- 确认网络连接正常
- 查看日志文件中的错误信息

#### 常见问题
1. **内容提取失败** - 检查crawl4ai服务状态
2. **翻译失败** - 验证API密钥和网络连接
3. **文件保存失败** - 确认输出目录权限
4. **编码问题** - 确保系统支持UTF-8

### 📈 使用建议

#### 日常使用
1. **首次使用** - 先运行功能演示了解效果
2. **定期爬取** - 设置定时任务每天自动运行
3. **特定需求** - 使用测试模式分析特定网站
4. **监控维护** - 定期检查日志和输出文件

#### 最佳实践
- 使用智能分析模式获得最佳效果
- 定期更新新闻网站配置
- 监控热度评分算法效果
- 根据需要调整翻译设置

### 🎯 功能演示

运行演示模式体验完整功能：
```bash
# 完整演示（包含翻译）
python demo_news_analyzer.py

# 快速演示（跳过翻译）
python demo_news_analyzer.py --no-translate
```

演示数据包含10条模拟新闻，展示：
- 热度评分算法
- 新闻排序逻辑
- 翻译功能
- 汇总文件生成

### 📞 技术支持

如遇问题，请检查：
1. **日志文件** - `philippines_news_crawler.log`
2. **服务状态** - crawl4ai是否正常运行
3. **网络连接** - API访问是否正常
4. **文件权限** - 输出目录是否可写

---

## 🎊 享受您的菲律宾新闻智能分析器！

**每天早上8点，您将自动获得：**
- 📈 热度最高的10条菲律宾新闻
- 🌐 完整的中文翻译
- 📊 详细的统计分析
- 📝 格式化的汇总报告

**让AI帮您掌握菲律宾最新动态！** 🇵🇭✨
