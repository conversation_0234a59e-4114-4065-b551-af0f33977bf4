# 脚本用于将 E:\mcp-test\MARK-out 文件夹中的 Third-party test report(1).docx 文件翻译成英文
# 按段落进行翻译，最后是中英文对照，保存在 A11.docx 同目录中

# 设置文件路径
$sourceFolder = "E:\mcp-test\MARK-out"
$sourceFile = Join-Path -Path $sourceFolder -ChildPath "Progress Report_template.docx"
$outputFile = Join-Path -Path $sourceFolder -ChildPath "A11.docx"

# 检查源文件是否存在
if (-not (Test-Path -Path $sourceFile)) {
    Write-Host "错误: 文件 $sourceFile 不存在!" -ForegroundColor Red
    exit
}

Write-Host "开始处理文件: $sourceFile" -ForegroundColor Cyan

# 创建临时Python脚本来处理文档翻译
$pythonScript = @'
import docx
from docx import Document
import sys
import os
import requests
import json
import time

def translate_text(text, source_lang='zh', target_lang='en'):
    """使用智谱GLM-4-Air-250414模型API翻译文本"""
    url = "https://open.bigmodel.cn/api/paas/v4/chat/completions"
    
    headers = {
        "Content-Type": "application/json",
        "Authorization": "Bearer 7810b54663644292bcfd221e9ec7f0dd.aPV0rs8StKq1FmD0"
    }
    
    # 构建提示词，要求模型将中文翻译成英文
    prompt = f"请将以下中文文本翻译成英文，只返回翻译结果，不要添加任何解释或额外内容：\n\n{text}"
    
    data = {
        "model": "GLM-4-Air-250414",
        "messages": [
            {"role": "user", "content": prompt}
        ],
        "temperature": 0.7,
        "max_tokens": 4096
    }
    
    try:
        response = requests.post(url, headers=headers, json=data)
        if response.status_code == 200:
            result = response.json()
            # 从响应中提取翻译后的文本
            translated_text = result["choices"][0]["message"]["content"]
            return translated_text
        else:
            print(f"翻译请求失败，状态码: {response.status_code}")
            print(f"错误信息: {response.text}")
            return None
    except Exception as e:
        print(f"翻译过程中出错: {str(e)}")
        return None

def translate_docx(input_file, output_file):
    """翻译docx文件并创建中英文对照版本"""
    try:
        # 读取源文档
        doc = Document(input_file)
        
        # 创建新文档
        new_doc = Document()
        
        # 添加标题
        new_doc.add_heading('双语对照文档 / Bilingual Document', 0)
        
        # 处理每个段落
        total_paragraphs = len(doc.paragraphs)
        for i, para in enumerate(doc.paragraphs):
            # 跳过空段落
            if not para.text.strip():
                new_doc.add_paragraph('')
                continue
            
            # 获取原文
            original_text = para.text.strip()
            
            # 打印进度
            print(f"正在翻译段落 {i+1}/{total_paragraphs}")
            
            # 翻译文本
            translated_text = translate_text(original_text)
            
            if translated_text:
                # 添加原文
                chinese_para = new_doc.add_paragraph(original_text)
                chinese_para.style = 'Normal'
                
                # 添加翻译
                english_para = new_doc.add_paragraph(translated_text)
                english_para.style = 'Normal'
                
                # 添加空行分隔
                new_doc.add_paragraph('')
            else:
                # 如果翻译失败，只添加原文
                new_doc.add_paragraph(original_text)
                new_doc.add_paragraph('[翻译失败 / Translation failed]')
                new_doc.add_paragraph('')
            
            # 添加延迟以避免API限制 - 减少等待时间以加快翻译速度
            time.sleep(0.01)  # 从1秒减少到0.1秒
        
        # 保存新文档
        new_doc.save(output_file)
        print(f"文档已成功翻译并保存到: {output_file}")
        return True
    except Exception as e:
        print(f"处理文档时出错: {str(e)}")
        return False

if __name__ == "__main__":
    if len(sys.argv) != 3:
        print("用法: python script.py 输入文件.docx 输出文件.docx")
        sys.exit(1)
    
    input_file = sys.argv[1]
    output_file = sys.argv[2]
    
    if not os.path.exists(input_file):
        print(f"错误: 输入文件 {input_file} 不存在")
        sys.exit(1)
    
    success = translate_docx(input_file, output_file)
    if not success:
        sys.exit(1)
'@

# 将Python脚本保存到临时文件
$tempPythonPath = [System.IO.Path]::Combine([System.IO.Path]::GetTempPath(), "translate_docx.py")
$pythonScript | Out-File -FilePath $tempPythonPath -Encoding utf8

Write-Host "正在翻译文档，这可能需要几分钟时间..." -ForegroundColor Yellow

# 执行Python脚本
try {
    python $tempPythonPath $sourceFile $outputFile
    
    if (Test-Path -Path $outputFile) {
        Write-Host "翻译完成! 结果已保存到: $outputFile" -ForegroundColor Green
    } else {
        Write-Host "翻译失败，未能创建输出文件" -ForegroundColor Red
    }
} catch {
    Write-Host "执行Python脚本时出错: $_" -ForegroundColor Red
}

# 清理临时文件
Remove-Item -Path $tempPythonPath -Force

Write-Host "处理完成!" -ForegroundColor Green