#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
菲律宾新闻分析爬虫 - 智能筛选和翻译版
支持新闻筛选、热度排序、翻译和汇总功能
"""

import requests
import os
import time
import datetime
import logging
import argparse
import json
import re
from pathlib import Path
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry
from enhanced_news_crawler import PhilippinesNewsCrawler

class NewsAnalyzer:
    def __init__(self, config_file="news_config.json"):
        """初始化新闻分析器"""
        self.base_crawler = PhilippinesNewsCrawler(config_file)
        self.config = self.base_crawler.config
        self.logger = self.base_crawler.logger
        self.session = self.base_crawler.session

        # 翻译API配置 - 使用用户偏好的GLM-4-Flash
        self.translation_config = {
            "api_key": "7810b54663644292bcfd221e9ecf7f0dd.aPV0rs8StKq1FmD0",
            "base_url": "https://open.bigmodel.cn/api/paas/v4",
            "model": "GLM-4-Flash"
        }

    def extract_individual_news(self, url, site_config=None):
        """提取网站中的个别新闻文章"""
        self.logger.info(f"开始提取新闻列表: {url}")

        if not self.base_crawler.check_crawl4ai_service():
            return []

        try:
            # 直接使用基础爬取方法，然后从内容中提取新闻
            return self.smart_news_extraction(url)

        except Exception as e:
            self.logger.error(f"提取新闻列表时出错: {str(e)}")
            return []

    def smart_news_extraction(self, url):
        """智能新闻提取方法"""
        # 使用基础爬虫获取页面内容
        data = self.base_crawler.crawl_website(url)
        if not data:
            self.logger.warning(f"基础爬虫未能获取内容: {url}")
            return []

        content = data.get("markdown", "")

        self.logger.info(f"获取到内容长度: {len(content)} 字符")

        if len(content) < 100:
            self.logger.warning(f"内容太少，尝试直接从crawl4ai获取HTML")
            return self.direct_html_extraction(url)

        news_items = []

        # 从Markdown内容中提取新闻标题
        lines = content.split("\n")
        current_news = {}

        for line in lines:
            line = line.strip()

            # 识别可能的新闻标题
            if self.is_likely_news_title(line):
                if current_news.get("title"):
                    # 保存前一条新闻
                    news_items.append(current_news)

                # 开始新的新闻项
                current_news = {
                    "title": self.clean_title(line),
                    "link": url,  # 默认链接
                    "summary": "",
                    "popularity_score": self.calculate_title_score(line),
                    "source": url
                }

            # 识别可能的摘要或描述
            elif current_news.get("title") and len(line) > 30 and not line.startswith("http"):
                if not current_news.get("summary"):
                    current_news["summary"] = line[:200]  # 限制摘要长度

        # 添加最后一条新闻
        if current_news.get("title"):
            news_items.append(current_news)

        # 去重和清理
        unique_news = self.deduplicate_news(news_items)

        self.logger.info(f"从内容中提取到 {len(unique_news)} 条新闻")

        # 限制数量并排序
        sorted_news = sorted(unique_news, key=lambda x: x["popularity_score"], reverse=True)
        return sorted_news[:20]

    def direct_html_extraction(self, url):
        """直接从crawl4ai获取HTML并提取新闻"""
        try:
            # 使用简单的crawl4ai请求
            payload = {"urls": [url]}
            api_url = self.config["crawl4ai"]["api_url"]
            response = self.session.post(api_url, json=payload, timeout=60)

            if response.status_code == 200:
                result = response.json()
                if result.get("success") and result.get("results"):
                    crawl_result = result["results"][0]
                    html_content = crawl_result.get("html", "")

                    if html_content:
                        self.logger.info(f"获取到HTML内容长度: {len(html_content)} 字符")
                        return self.extract_from_html(html_content, url)

            return []

        except Exception as e:
            self.logger.error(f"直接HTML提取失败: {str(e)}")
            return []

    def is_likely_news_title(self, line):
        """判断是否可能是新闻标题"""
        if len(line) < 15 or len(line) > 200:
            return False

        # 排除明显不是标题的内容
        exclude_patterns = [
            "http", "www.", "cookie", "privacy", "terms", "contact",
            "subscribe", "newsletter", "advertisement", "©", "copyright",
            "follow us", "social media", "facebook", "twitter", "instagram",
            "javascript", "function", "var ", "window", "document", "script",
            "css", "font-family", "background", "margin", "padding", "color:",
            "display:", "position:", "width:", "height:", "border:", "font-size:",
            "googletag", "dataLayer", "analytics", "tracking", "pixel"
        ]

        line_lower = line.lower()
        for pattern in exclude_patterns:
            if pattern in line_lower:
                return False

        # 排除CSS和JavaScript代码
        if any(char in line for char in ['{', '}', ';', '()', '[]', '&&', '||']):
            return False

        # 排除包含大量数字或特殊字符的行
        special_char_count = sum(1 for char in line if char in '!@#$%^&*()_+-=[]{}|;:,.<>?')
        if special_char_count > len(line) * 0.3:  # 超过30%是特殊字符
            return False

        # 包含新闻关键词的更可能是标题
        news_keywords = [
            "president", "government", "manila", "philippines", "filipino",
            "says", "announces", "reports", "breaking", "news", "update",
            "covid", "economy", "election", "police", "court", "senate",
            "killed", "died", "arrested", "charged", "convicted", "sentenced",
            "typhoon", "earthquake", "fire", "accident", "crash", "explosion",
            "budget", "tax", "inflation", "peso", "dollar", "investment",
            "duterte", "marcos", "bongbong", "sara", "robredo", "aquino"
        ]

        for keyword in news_keywords:
            if keyword in line_lower:
                return True

        # 基本格式检查 - 新闻标题通常以大写字母开头，包含标点符号
        if line[0].isupper() and any(punct in line for punct in '.!?:'):
            # 确保不是代码或配置
            if not any(code_indicator in line_lower for code_indicator in ['=', ':', '{', '}', '()', 'function', 'var']):
                return True

        return False

    def clean_title(self, title):
        """清理标题"""
        # 移除Markdown标记
        title = re.sub(r'^#+\s*', '', title)
        title = re.sub(r'\*+', '', title)
        title = re.sub(r'_+', '', title)

        # 移除多余的空白
        title = re.sub(r'\s+', ' ', title).strip()

        return title

    def calculate_title_score(self, title):
        """基于标题计算热度分数"""
        score = 10  # 基础分数

        title_lower = title.lower()

        # 重要关键词加分
        important_keywords = {
            "breaking": 20, "urgent": 15, "exclusive": 15,
            "president": 10, "government": 8, "manila": 5,
            "covid": 8, "pandemic": 8, "economy": 6,
            "election": 10, "senate": 6, "congress": 6,
            "typhoon": 12, "earthquake": 12, "disaster": 10,
            "killed": 8, "death": 8, "accident": 6,
            "arrest": 6, "police": 5, "court": 5
        }

        for keyword, points in important_keywords.items():
            if keyword in title_lower:
                score += points

        # 标题长度适中加分
        if 20 <= len(title) <= 100:
            score += 5

        return score

    def extract_from_html(self, html_content, base_url):
        """从HTML中提取新闻链接和标题"""
        news_items = []

        try:
            # 简单的HTML解析，查找链接和标题
            import re

            # 查找所有链接
            link_pattern = r'<a[^>]+href=["\']([^"\']+)["\'][^>]*>([^<]+)</a>'
            links = re.findall(link_pattern, html_content, re.IGNORECASE)

            for href, text in links:
                text = text.strip()
                if self.is_likely_news_title(text):
                    # 处理相对链接
                    if href.startswith('/'):
                        from urllib.parse import urljoin
                        full_url = urljoin(base_url, href)
                    elif href.startswith('http'):
                        full_url = href
                    else:
                        continue

                    news_items.append({
                        "title": self.clean_title(text),
                        "link": full_url,
                        "summary": "",
                        "popularity_score": self.calculate_title_score(text),
                        "source": base_url
                    })

        except Exception as e:
            self.logger.warning(f"HTML解析出错: {str(e)}")

        return news_items

    def deduplicate_news(self, news_items):
        """去除重复的新闻"""
        seen_titles = set()
        unique_news = []

        for news in news_items:
            title = news.get("title", "").lower().strip()
            if title and len(title) > 10 and title not in seen_titles:
                seen_titles.add(title)
                unique_news.append(news)

        return unique_news

    def process_news_list(self, news_data, source_url):
        """处理提取的新闻列表数据"""
        processed_news = []

        if isinstance(news_data, list):
            articles = news_data
        elif isinstance(news_data, dict) and "articles" in news_data:
            articles = news_data["articles"]
        else:
            return []

        for article in articles:
            if not isinstance(article, dict):
                continue

            title = article.get("title", "").strip()
            link = article.get("link", "").strip()

            if not title or len(title) < 10:  # 过滤太短的标题
                continue

            # 处理相对链接
            if link and not link.startswith("http"):
                from urllib.parse import urljoin
                link = urljoin(source_url, link)

            # 提取热度指标
            popularity_score = self.calculate_popularity_score(article)

            processed_article = {
                "title": title,
                "link": link,
                "summary": article.get("summary", "").strip(),
                "author": article.get("author", "").strip(),
                "date": article.get("date", "").strip(),
                "category": article.get("category", "").strip(),
                "views": self.extract_number(article.get("views", "")),
                "comments": self.extract_number(article.get("comments", "")),
                "popularity_score": popularity_score,
                "source": source_url
            }

            processed_news.append(processed_article)

        return processed_news

    def calculate_popularity_score(self, article):
        """计算新闻的热度分数"""
        score = 0

        # 基于浏览量
        views = self.extract_number(article.get("views", ""))
        if views > 0:
            score += min(views / 1000, 50)  # 最多50分

        # 基于评论数
        comments = self.extract_number(article.get("comments", ""))
        if comments > 0:
            score += min(comments * 2, 30)  # 最多30分

        # 基于标题关键词
        title = article.get("title", "").lower()
        important_keywords = [
            "breaking", "urgent", "exclusive", "major", "crisis", "emergency",
            "president", "government", "election", "economy", "covid", "pandemic",
            "typhoon", "earthquake", "disaster", "accident", "death", "killed"
        ]

        for keyword in important_keywords:
            if keyword in title:
                score += 10

        # 基于发布时间（越新越重要）
        date_str = article.get("date", "")
        if self.is_recent_news(date_str):
            score += 20

        return score

    def extract_number(self, text):
        """从文本中提取数字"""
        if not text:
            return 0

        # 查找数字
        numbers = re.findall(r'\d+', str(text))
        if numbers:
            return int(numbers[0])
        return 0

    def is_recent_news(self, date_str):
        """判断是否为最近的新闻"""
        if not date_str:
            return False

        try:
            # 简单的时间判断逻辑
            now = datetime.datetime.now()
            today = now.strftime("%Y-%m-%d")
            yesterday = (now - datetime.timedelta(days=1)).strftime("%Y-%m-%d")

            return today in date_str or yesterday in date_str
        except:
            return False

    def fallback_news_extraction(self, url):
        """回退的新闻提取方法"""
        # 使用基础爬虫获取页面内容
        data = self.base_crawler.crawl_website(url)
        if not data:
            return []

        # 简单的新闻提取逻辑
        content = data.get("markdown", "")
        lines = content.split("\n")

        news_items = []
        current_title = ""

        for line in lines:
            line = line.strip()
            if len(line) > 20 and ("##" in line or line.isupper() or ":" in line):
                if current_title:
                    news_items.append({
                        "title": current_title,
                        "link": url,
                        "summary": "",
                        "popularity_score": 5,  # 默认分数
                        "source": url
                    })
                current_title = line.replace("#", "").strip()

        return news_items[:20]  # 最多返回20条

    def translate_text(self, text, target_language="中文"):
        """使用GLM-4-Flash翻译文本"""
        if not text or len(text.strip()) < 5:
            return text

        try:
            headers = {
                "Authorization": f"Bearer {self.translation_config['api_key']}",
                "Content-Type": "application/json"
            }

            prompt = f"请将以下英文新闻翻译成{target_language}，保持新闻的准确性和可读性：\n\n{text}"

            payload = {
                "model": self.translation_config["model"],
                "messages": [
                    {"role": "user", "content": prompt}
                ],
                "temperature": 0.3,
                "max_tokens": 2000
            }

            response = requests.post(
                f"{self.translation_config['base_url']}/chat/completions",
                headers=headers,
                json=payload,
                timeout=30
            )

            if response.status_code == 200:
                result = response.json()
                translated_text = result["choices"][0]["message"]["content"]
                return translated_text.strip()
            else:
                self.logger.error(f"翻译API请求失败: {response.status_code}")
                return text

        except Exception as e:
            self.logger.error(f"翻译过程中出错: {str(e)}")
            return text

    def get_full_article_content(self, article_url):
        """获取完整的文章内容"""
        try:
            data = self.base_crawler.crawl_website(article_url)
            if data and data.get("markdown"):
                content = data["markdown"]
                # 清理内容，移除过多的空行和无关信息
                lines = content.split("\n")
                cleaned_lines = []

                for line in lines:
                    line = line.strip()
                    if len(line) > 10 and not line.startswith("http") and "cookie" not in line.lower():
                        cleaned_lines.append(line)

                return "\n".join(cleaned_lines[:50])  # 限制长度
            return ""
        except Exception as e:
            self.logger.error(f"获取文章内容失败: {str(e)}")
            return ""

    def crawl_and_analyze_all_sites(self):
        """爬取所有网站并分析新闻"""
        timestamp = self.base_crawler.get_timestamp()
        self.logger.info(f"开始新闻分析任务: {timestamp}")

        all_news = []
        sites = self.config["philippines_news_sites"]
        enabled_sites = [site for site in sites if site.get("enabled", True)]

        for site_config in enabled_sites:
            url = site_config["url"]
            self.logger.info(f"分析网站: {url}")

            news_list = self.extract_individual_news(url, site_config)
            if news_list:
                all_news.extend(news_list)
                self.logger.info(f"从 {url} 提取了 {len(news_list)} 条新闻")

            # 添加延迟
            time.sleep(3)

        # 按热度排序并选择前10条
        sorted_news = sorted(all_news, key=lambda x: x["popularity_score"], reverse=True)
        top_news = sorted_news[:10]

        self.logger.info(f"总共提取 {len(all_news)} 条新闻，选择热度最高的 {len(top_news)} 条")

        # 获取完整内容并翻译
        translated_news = []
        for i, news in enumerate(top_news, 1):
            self.logger.info(f"处理第 {i} 条新闻: {news['title'][:50]}...")

            # 获取完整内容
            if news.get("link") and news["link"] != news["source"]:
                full_content = self.get_full_article_content(news["link"])
                if full_content:
                    news["full_content"] = full_content

            # 翻译标题和摘要
            news["title_zh"] = self.translate_text(news["title"])
            if news.get("summary"):
                news["summary_zh"] = self.translate_text(news["summary"])

            # 翻译部分内容
            if news.get("full_content"):
                # 只翻译前500字符以节省API调用
                content_to_translate = news["full_content"][:500]
                news["content_zh"] = self.translate_text(content_to_translate)

            translated_news.append(news)
            time.sleep(2)  # 避免API限制

        # 生成汇总文件
        self.generate_summary_file(translated_news, timestamp)

        return translated_news

    def generate_summary_file(self, news_list, timestamp):
        """生成汇总文件"""
        date_str = datetime.datetime.now().strftime("%Y年%m月%d日")
        filename = f"{timestamp}_汇总.md"
        output_dir = self.config["output"]["directory"]
        filepath = os.path.join(output_dir, filename)

        try:
            with open(filepath, "w", encoding="utf-8") as f:
                # 写入标题和元数据
                f.write("---\n")
                f.write(f"title: \"菲律宾新闻热点汇总 - {date_str}\"\n")
                f.write(f"date: {timestamp.replace('_', ' ')}\n")
                f.write(f"type: summary\n")
                f.write(f"news_count: {len(news_list)}\n")
                f.write("tags: [news, philippines, summary, translated]\n")
                f.write("---\n\n")

                # 写入汇总标题
                f.write(f"# 菲律宾新闻热点汇总 - {date_str}\n\n")
                f.write(f"**生成时间**: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"**新闻数量**: {len(news_list)} 条\n")
                f.write(f"**排序方式**: 按热度排序（浏览量、评论数、关键词等综合评分）\n\n")

                f.write("---\n\n")

                # 写入新闻列表
                for i, news in enumerate(news_list, 1):
                    f.write(f"## {i}. {news.get('title_zh', news['title'])}\n\n")

                    # 基本信息
                    f.write(f"**原标题**: {news['title']}\n")
                    f.write(f"**来源**: {news['source']}\n")
                    f.write(f"**热度评分**: {news['popularity_score']:.1f}\n")

                    if news.get("author"):
                        f.write(f"**作者**: {news['author']}\n")
                    if news.get("date"):
                        f.write(f"**发布时间**: {news['date']}\n")
                    if news.get("category"):
                        f.write(f"**分类**: {news['category']}\n")
                    if news.get("views", 0) > 0:
                        f.write(f"**浏览量**: {news['views']}\n")
                    if news.get("comments", 0) > 0:
                        f.write(f"**评论数**: {news['comments']}\n")

                    f.write("\n")

                    # 摘要
                    if news.get("summary_zh"):
                        f.write(f"**摘要**: {news['summary_zh']}\n\n")
                    elif news.get("summary"):
                        f.write(f"**摘要**: {news['summary']}\n\n")

                    # 翻译内容
                    if news.get("content_zh"):
                        f.write(f"**内容摘要**:\n{news['content_zh']}\n\n")

                    # 原文链接
                    if news.get("link") and news["link"] != news["source"]:
                        f.write(f"**原文链接**: [{news['title']}]({news['link']})\n\n")

                    f.write("---\n\n")

                # 写入统计信息
                f.write("## 统计信息\n\n")
                sources = {}
                for news in news_list:
                    source = news["source"]
                    sources[source] = sources.get(source, 0) + 1

                f.write("**新闻来源分布**:\n")
                for source, count in sources.items():
                    domain = source.replace("https://", "").replace("http://", "").split("/")[0]
                    f.write(f"- {domain}: {count} 条\n")

                f.write(f"\n**平均热度评分**: {sum(news['popularity_score'] for news in news_list) / len(news_list):.1f}\n")

            self.logger.info(f"汇总文件已生成: {filepath}")

        except Exception as e:
            self.logger.error(f"生成汇总文件时出错: {str(e)}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="菲律宾新闻分析爬虫 - 智能筛选和翻译版")
    parser.add_argument("--config", default="news_config.json", help="配置文件路径")
    parser.add_argument("--mode", choices=["analyze", "test"], default="analyze",
                       help="运行模式: analyze=分析并生成汇总, test=测试单个网站")
    parser.add_argument("--url", help="测试模式下要分析的URL")
    parser.add_argument("--no-translate", action="store_true", help="跳过翻译步骤")

    args = parser.parse_args()

    # 创建分析器实例
    analyzer = NewsAnalyzer(args.config)

    if args.mode == "analyze":
        # 完整分析模式
        analyzer.logger.info("开始完整新闻分析和汇总")
        print("🚀 开始菲律宾新闻热点分析...")
        print("📊 这将包括：新闻提取 → 热度排序 → 翻译 → 生成汇总")
        print("⏱️  预计需要 5-10 分钟，请耐心等待...\n")

        try:
            translated_news = analyzer.crawl_and_analyze_all_sites()

            print(f"\n✅ 分析完成！")
            print(f"📈 共分析了 {len(translated_news)} 条热点新闻")
            print(f"📁 汇总文件已保存到: {analyzer.config['output']['directory']}")
            print(f"📝 文件名格式: YYYY-MM-DD_HH-MM_汇总.md")

            # 显示前3条新闻标题
            if translated_news:
                print(f"\n🔥 热度最高的前3条新闻:")
                for i, news in enumerate(translated_news[:3], 1):
                    title_zh = news.get('title_zh', news['title'])
                    score = news['popularity_score']
                    print(f"  {i}. {title_zh} (热度: {score:.1f})")

        except Exception as e:
            analyzer.logger.error(f"分析过程中出错: {str(e)}")
            print(f"❌ 分析失败: {str(e)}")

    elif args.mode == "test":
        # 测试模式
        if not args.url:
            print("❌ 测试模式需要指定 --url 参数")
            return

        analyzer.logger.info(f"测试模式: 分析单个网站 {args.url}")
        print(f"🧪 测试模式: 分析 {args.url}")

        try:
            news_list = analyzer.extract_individual_news(args.url)

            if news_list:
                print(f"✅ 成功提取 {len(news_list)} 条新闻")
                print("\n📰 新闻列表 (按热度排序):")

                sorted_news = sorted(news_list, key=lambda x: x["popularity_score"], reverse=True)
                for i, news in enumerate(sorted_news[:5], 1):
                    print(f"  {i}. {news['title'][:60]}... (热度: {news['popularity_score']:.1f})")

                if not args.no_translate and sorted_news:
                    print(f"\n🌐 翻译测试 (第1条新闻):")
                    first_news = sorted_news[0]
                    translated_title = analyzer.translate_text(first_news['title'])
                    print(f"  原文: {first_news['title']}")
                    print(f"  译文: {translated_title}")
            else:
                print("❌ 未能提取到新闻")

        except Exception as e:
            analyzer.logger.error(f"测试过程中出错: {str(e)}")
            print(f"❌ 测试失败: {str(e)}")

    print(f"\n📋 使用说明:")
    print(f"  完整分析: python {os.path.basename(__file__)} --mode analyze")
    print(f"  测试网站: python {os.path.basename(__file__)} --mode test --url <URL>")
    print(f"  跳过翻译: python {os.path.basename(__file__)} --mode analyze --no-translate")


if __name__ == "__main__":
    main()
