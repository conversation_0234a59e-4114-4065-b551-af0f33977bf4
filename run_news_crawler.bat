@echo off
chcp 65001 > nul
echo 菲律宾新闻爬虫启动脚本
echo ========================

:menu
echo.
echo 请选择运行模式:
echo 1. 立即爬取一次所有新闻网站
echo 2. 启动定时任务 (每天早上8点自动运行)
echo 3. 爬取指定URL
echo 4. 检查crawl4ai服务状态
echo 5. 启动crawl4ai服务
echo 6. 退出
echo.

set /p choice=请输入选择 (1-6): 

if "%choice%"=="1" goto once
if "%choice%"=="2" goto schedule
if "%choice%"=="3" goto url
if "%choice%"=="4" goto check_service
if "%choice%"=="5" goto start_service
if "%choice%"=="6" goto exit
echo 无效选择，请重新输入
goto menu

:once
echo 开始单次爬取...
python philippines_news_crawler.py --mode once
pause
goto menu

:schedule
echo 启动定时任务模式...
echo 注意: 按 Ctrl+C 可以停止定时任务
python philippines_news_crawler.py --mode schedule
pause
goto menu

:url
set /p target_url=请输入要爬取的URL: 
if "%target_url%"=="" (
    echo URL不能为空
    pause
    goto menu
)
echo 开始爬取: %target_url%
python philippines_news_crawler.py --mode url --url "%target_url%"
pause
goto menu

:check_service
echo 检查crawl4ai服务状态...
docker ps | findstr crawl4ai
if %errorlevel%==0 (
    echo crawl4ai服务正在运行
) else (
    echo crawl4ai服务未运行
)
pause
goto menu

:start_service
echo 启动crawl4ai服务...
docker start crawl4ai
if %errorlevel%==0 (
    echo crawl4ai服务启动成功
) else (
    echo crawl4ai服务启动失败，请检查Docker是否正在运行
)
pause
goto menu

:exit
echo 再见！
exit /b 0
