#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
菲律宾新闻爬虫 - 增强版
支持配置文件、定时任务、命令行接口等功能
"""

import requests
import os
import time
import datetime
import logging
import argparse
import schedule
import threading
import json
import re
from pathlib import Path
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

class PhilippinesNewsCrawler:
    def __init__(self, config_file="news_config.json"):
        """初始化爬虫"""
        self.config = self.load_config(config_file)
        self.setup_logging()
        self.session = self.create_session()

    def load_config(self, config_file):
        """加载配置文件"""
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            print(f"配置文件 {config_file} 不存在，使用默认配置")
            return self.get_default_config()
        except json.JSONDecodeError as e:
            print(f"配置文件格式错误: {e}")
            return self.get_default_config()

    def get_default_config(self):
        """获取默认配置"""
        return {
            "crawl4ai": {
                "api_url": "http://localhost:11235/crawl",
                "health_check_url": "http://localhost:11235/health",
                "timeout": 120,
                "retry_attempts": 3
            },
            "output": {
                "directory": r"E:\mcp-test\obsidian wang\newspaper",
                "filename_format": "{timestamp}_{site_name}.md",
                "encoding": "utf-8"
            },
            "philippines_news_sites": [
                {"name": "菲律宾每日询问者", "url": "https://www.inquirer.net/", "enabled": True},
                {"name": "菲律宾星报", "url": "https://www.philstar.com/", "enabled": True},
                {"name": "GMA新闻", "url": "https://www.gmanetwork.com/news/", "enabled": True},
                {"name": "ABS-CBN新闻", "url": "https://news.abs-cbn.com/", "enabled": True},
                {"name": "Rappler", "url": "https://www.rappler.com/", "enabled": True}
            ],
            "crawl_settings": {
                "word_count_threshold": 50,
                "include_links": True,
                "include_images": True,
                "wait_for_seconds": 3,
                "remove_overlay": True,
                "cache_mode": "bypass",
                "delay_between_requests": 5
            }
        }

    def setup_logging(self):
        """设置日志"""
        log_config = self.config.get("logging", {})
        logging.basicConfig(
            level=getattr(logging, log_config.get("level", "INFO")),
            format=log_config.get("format", "%(asctime)s - %(levelname)s - %(message)s"),
            handlers=[
                logging.FileHandler(log_config.get("file", "philippines_news_crawler.log"), encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)

    def create_session(self):
        """创建带重试机制的会话"""
        session = requests.Session()
        retry_config = Retry(
            total=self.config["crawl4ai"].get("retry_attempts", 3),
            backoff_factor=0.5,
            status_forcelist=[500, 502, 503, 504]
        )
        adapter = HTTPAdapter(max_retries=retry_config)
        session.mount('http://', adapter)
        session.mount('https://', adapter)
        return session

    def ensure_output_dir(self):
        """确保输出目录存在"""
        output_dir = self.config["output"]["directory"]
        Path(output_dir).mkdir(parents=True, exist_ok=True)

    def get_timestamp(self):
        """获取当前时间戳"""
        return datetime.datetime.now().strftime("%Y-%m-%d_%H-%M")

    def check_crawl4ai_service(self):
        """检查crawl4ai服务是否可用"""
        try:
            health_url = self.config["crawl4ai"]["health_check_url"]
            response = self.session.get(health_url, timeout=5)
            if response.status_code == 200:
                return True
            else:
                self.logger.error(f"crawl4ai服务返回状态码: {response.status_code}")
                return False
        except requests.exceptions.RequestException as e:
            self.logger.error(f"无法连接到crawl4ai服务: {str(e)}")
            self.logger.info("请确保crawl4ai服务正在运行，可以通过以下命令启动: docker start crawl4ai")
            return False

    def crawl_website(self, url, site_config=None):
        """爬取指定网站的新闻"""
        self.logger.info(f"开始爬取: {url}")

        if not self.check_crawl4ai_service():
            return None

        try:
            crawl_settings = self.config["crawl_settings"]

            # 准备请求参数 - 根据crawl4ai API格式
            payload = {
                "urls": [url],  # crawl4ai需要urls数组
                "extraction_config": {
                    "type": "markdown",
                    "params": {
                        "css_selector": site_config.get("css_selector", "article, .news-item, .story, .headline, .article-item, .post, .entry, .content-item") if site_config else "article, .news-item, .story, .headline, .article-item, .post, .entry, .content-item",
                        "word_count_threshold": crawl_settings.get("word_count_threshold", 50),
                        "include_links": crawl_settings.get("include_links", True),
                        "include_images": crawl_settings.get("include_images", True)
                    }
                },
                "crawler_config": {
                    "wait_for": crawl_settings.get("wait_for_seconds", 3),
                    "timeout": self.config["crawl4ai"].get("timeout", 60),
                    "remove_overlay": crawl_settings.get("remove_overlay", True),
                    "screenshot": False,
                    "cache_mode": crawl_settings.get("cache_mode", "bypass")
                }
            }

            # 发送请求到crawl4ai API
            api_url = self.config["crawl4ai"]["api_url"]
            response = self.session.post(api_url, json=payload, timeout=self.config["crawl4ai"]["timeout"])

            if response.status_code == 200:
                result = response.json()
                # crawl4ai返回格式: {"success": true, "results": [...]}
                if result.get("success") and result.get("results"):
                    crawl_result = result["results"][0]  # 取第一个结果

                    # 从HTML生成markdown (如果没有直接的markdown字段)
                    html_content = crawl_result.get("html", "")
                    markdown_content = crawl_result.get("markdown", "")

                    # 如果没有markdown，尝试从HTML提取
                    if not markdown_content and html_content:
                        # 简单的HTML到文本转换
                        # 移除HTML标签
                        text_content = re.sub(r'<[^>]+>', '', html_content)
                        # 清理多余的空白
                        markdown_content = re.sub(r'\s+', ' ', text_content).strip()

                    # 如果内容太少，尝试获取更多内容
                    if len(markdown_content.strip()) < 500:
                        self.logger.warning(f"内容较少 ({len(markdown_content)} 字符)，尝试简化请求")
                        # 简化请求，只要基本的HTML内容
                        simple_payload = {"urls": [url]}
                        response = self.session.post(api_url, json=simple_payload, timeout=self.config["crawl4ai"]["timeout"])
                        if response.status_code == 200:
                            result = response.json()
                            if result.get("success") and result.get("results"):
                                crawl_result = result["results"][0]
                                html_content = crawl_result.get("html", "")
                                if html_content:
                                    text_content = re.sub(r'<[^>]+>', '', html_content)
                                    markdown_content = re.sub(r'\s+', ' ', text_content).strip()

                    return {
                        "site": url,
                        "markdown": markdown_content,
                        "title": crawl_result.get("title", "未知标题"),
                        "links": crawl_result.get("links", []),
                        "images": crawl_result.get("images", []),
                        "metadata": crawl_result.get("metadata", {})
                    }
                else:
                    error_msg = result.get("error", "未知错误")
                    self.logger.error(f"爬取失败: {error_msg}")
                    self.logger.debug(f"完整响应: {result}")
            else:
                self.logger.error(f"API请求失败: {response.status_code}")
                try:
                    error_detail = response.text
                    self.logger.debug(f"错误详情: {error_detail}")
                except:
                    pass

        except Exception as e:
            self.logger.error(f"爬取过程中出错: {str(e)}")

        return None

    def save_to_markdown(self, data, timestamp=None):
        """将爬取的内容保存为Markdown文件"""
        if not data or not data.get("markdown"):
            return False

        if timestamp is None:
            timestamp = self.get_timestamp()

        site_name = data["site"].replace("https://", "").replace("http://", "").split("/")[0]
        filename = f"{timestamp}_{site_name}.md"
        output_dir = self.config["output"]["directory"]
        filepath = os.path.join(output_dir, filename)

        try:
            with open(filepath, "w", encoding=self.config["output"]["encoding"]) as f:
                # 添加YAML前置元数据
                f.write("---\n")
                f.write(f"title: \"{data['title']}\"\n")
                f.write(f"source: {data['site']}\n")
                f.write(f"date: {timestamp.replace('_', ' ')}\n")
                f.write(f"crawl_time: {datetime.datetime.now().isoformat()}\n")
                f.write(f"tags: [news, philippines, {site_name}]\n")

                # 添加元数据信息
                metadata = data.get("metadata", {})
                if metadata:
                    f.write(f"description: \"{metadata.get('description', '')}\"\n")
                    f.write(f"keywords: \"{metadata.get('keywords', '')}\"\n")
                    f.write(f"author: \"{metadata.get('author', '')}\"\n")

                # 添加统计信息
                content_length = len(data["markdown"])
                f.write(f"content_length: {content_length}\n")
                f.write(f"links_count: {len(data.get('links', []))}\n")
                f.write(f"images_count: {len(data.get('images', []))}\n")
                f.write("---\n\n")

                # 添加标题和来源
                f.write(f"# {data['title']}\n\n")
                f.write(f"**来源**: [{site_name}]({data['site']})\n")
                f.write(f"**爬取时间**: {timestamp.replace('_', ' ')}\n")
                f.write(f"**内容长度**: {content_length} 字符\n\n")

                # 如果有描述，添加描述
                if metadata.get("description"):
                    f.write(f"**描述**: {metadata['description']}\n\n")

                f.write("---\n\n")

                # 添加主要内容
                f.write(data["markdown"])

                # 如果有链接，添加链接列表
                links = data.get("links", [])
                if links and len(links) > 0:
                    f.write("\n\n## 相关链接\n\n")
                    # 确保links是列表，并且只取前10个
                    if isinstance(links, list):
                        limited_links = links[:10]
                    else:
                        limited_links = []

                    for i, link in enumerate(limited_links):
                        if isinstance(link, dict):
                            url = link.get("href", "")
                            text = link.get("text", url)
                        else:
                            url = str(link)
                            text = url
                        if url:
                            f.write(f"{i+1}. [{text}]({url})\n")

            self.logger.info(f"已保存: {filepath} (内容长度: {content_length} 字符)")
            return True
        except Exception as e:
            self.logger.error(f"保存文件时出错: {str(e)}")
            return False

    def crawl_all_sites(self):
        """爬取所有配置的菲律宾新闻网站"""
        timestamp = self.get_timestamp()
        self.logger.info(f"开始每日新闻爬取任务: {timestamp}")

        self.ensure_output_dir()
        success_count = 0

        # 首先检查服务是否可用
        if not self.check_crawl4ai_service():
            self.logger.error("crawl4ai服务不可用，爬取任务取消")
            return 0

        sites = self.config["philippines_news_sites"]
        enabled_sites = [site for site in sites if site.get("enabled", True)]

        for site_config in enabled_sites:
            url = site_config["url"]
            data = self.crawl_website(url, site_config)
            if data and self.save_to_markdown(data, timestamp):
                success_count += 1

            # 添加延迟以避免过快请求
            delay = self.config["crawl_settings"].get("delay_between_requests", 5)
            time.sleep(delay)

        self.logger.info(f"每日爬取任务完成: 成功爬取 {success_count}/{len(enabled_sites)} 个网站")
        return success_count

    def crawl_specific_url(self, url):
        """爬取指定URL的内容"""
        self.logger.info(f"开始爬取指定URL: {url}")
        self.ensure_output_dir()

        # 首先检查服务是否可用
        if not self.check_crawl4ai_service():
            self.logger.error("crawl4ai服务不可用，爬取任务取消")
            return False

        data = self.crawl_website(url)
        if data and self.save_to_markdown(data):
            self.logger.info(f"成功爬取并保存: {url}")
            return True
        else:
            self.logger.error(f"爬取或保存失败: {url}")
            return False

    def run_scheduler(self):
        """运行定时任务调度器"""
        schedule_time = self.config.get("schedule", {}).get("daily_time", "08:00")
        self.logger.info(f"定时任务调度器启动，每天{schedule_time}自动爬取新闻")

        # 设置每天指定时间运行
        schedule.every().day.at(schedule_time).do(self.crawl_all_sites)

        while True:
            schedule.run_pending()
            time.sleep(60)  # 每分钟检查一次

    def run_scheduler_in_background(self):
        """在后台线程中运行调度器"""
        scheduler_thread = threading.Thread(target=self.run_scheduler, daemon=True)
        scheduler_thread.start()
        return scheduler_thread


def main():
    """主函数 - 支持命令行参数"""
    parser = argparse.ArgumentParser(description="菲律宾新闻爬虫 - 增强版")
    parser.add_argument("--config", default="news_config.json", help="配置文件路径")
    parser.add_argument("--mode", choices=["once", "schedule", "url"], default="once",
                       help="运行模式: once=运行一次, schedule=定时任务, url=爬取指定URL")
    parser.add_argument("--url", help="要爬取的URL (当mode=url时使用)")
    parser.add_argument("--daemon", action="store_true", help="以守护进程模式运行定时任务")

    args = parser.parse_args()

    # 创建爬虫实例
    crawler = PhilippinesNewsCrawler(args.config)

    if args.mode == "once":
        # 立即运行一次爬取
        crawler.logger.info("运行模式: 单次爬取")
        success_count = crawler.crawl_all_sites()
        print(f"爬取完成！成功爬取 {success_count} 个网站的内容。")

    elif args.mode == "schedule":
        if args.daemon:
            # 守护进程模式
            crawler.logger.info("运行模式: 定时任务 (守护进程)")
            try:
                crawler.run_scheduler()
            except KeyboardInterrupt:
                crawler.logger.info("收到中断信号，停止定时任务")
        else:
            # 交互模式
            crawler.logger.info("运行模式: 定时任务 (交互模式)")
            schedule_time = crawler.config.get("schedule", {}).get("daily_time", "08:00")
            print(f"定时任务已启动，每天{schedule_time}自动爬取新闻")
            print("按 Ctrl+C 停止...")

            scheduler_thread = crawler.run_scheduler_in_background()

            try:
                # 立即运行一次
                print("立即执行一次爬取任务...")
                success_count = crawler.crawl_all_sites()
                print(f"立即爬取完成！成功爬取 {success_count} 个网站的内容。")

                # 保持主线程运行
                while True:
                    time.sleep(1)
            except KeyboardInterrupt:
                crawler.logger.info("收到中断信号，停止定时任务")

    elif args.mode == "url":
        if not args.url:
            print("错误: 使用 --mode url 时必须指定 --url 参数")
            return

        crawler.logger.info(f"运行模式: 爬取指定URL - {args.url}")
        success = crawler.crawl_specific_url(args.url)
        if success:
            print("爬取成功！内容已保存到指定目录。")
        else:
            print("爬取失败，请查看日志文件了解详情。")

    # 提示用户其他使用方式
    if args.mode == "once":
        print("\n其他使用方式:")
        print(f"  定时任务: python {os.path.basename(__file__)} --mode schedule")
        print(f"  爬取指定URL: python {os.path.basename(__file__)} --mode url --url <URL>")
        print(f"  守护进程: python {os.path.basename(__file__)} --mode schedule --daemon")
        print(f"  自定义配置: python {os.path.basename(__file__)} --config my_config.json")
        print("\n如果爬取失败，请确保crawl4ai服务正在运行:")
        print("- 检查服务: docker ps | findstr crawl4ai")
        print("- 启动服务: docker start crawl4ai")


if __name__ == "__main__":
    main()