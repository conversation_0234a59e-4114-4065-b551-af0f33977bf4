# 菲律宾新闻爬虫

这是一个专门用于爬取菲律宾主要新闻网站的爬虫程序，支持定时任务、命令行操作等多种功能。

## 功能特点

- 🕐 **定时任务**: 每天早上8点自动爬取最新新闻
- 🌐 **多网站支持**: 支持菲律宾主要新闻门户网站
- 📝 **Markdown格式**: 自动保存为Obsidian兼容的Markdown格式
- ⚙️ **配置文件**: 支持JSON配置文件，方便自定义
- 🔄 **重试机制**: 内置重试和错误处理机制
- 📊 **详细日志**: 完整的日志记录和统计信息

## 支持的新闻网站

- 菲律宾每日询问者 (Inquirer.net)
- 菲律宾星报 (PhilStar.com)
- GMA新闻 (GMANetwork.com)
- ABS-CBN新闻 (ABS-CBN.com)
- Rappler (Rappler.com)
- Manila Bulletin (MB.com.ph)
- BusinessWorld (BWWorldOnline.com)

## 安装要求

### 前置条件
1. Python 3.7+
2. Docker (用于运行crawl4ai服务)
3. crawl4ai服务已部署并运行在 `localhost:11235`

### Python依赖
```bash
pip install requests schedule
```

## 使用方法

### 1. 基础版本 (philippines_news_crawler.py)

#### 单次运行
```bash
python philippines_news_crawler.py
```

#### 定时任务模式
```bash
python philippines_news_crawler.py --mode schedule
```

#### 爬取指定URL
```bash
python philippines_news_crawler.py --mode url --url "https://www.inquirer.net/"
```

### 2. 增强版本 (enhanced_news_crawler.py)

#### 使用默认配置运行一次
```bash
python enhanced_news_crawler.py
```

#### 使用自定义配置文件
```bash
python enhanced_news_crawler.py --config my_config.json
```

#### 启动定时任务
```bash
python enhanced_news_crawler.py --mode schedule
```

#### 守护进程模式
```bash
python enhanced_news_crawler.py --mode schedule --daemon
```

### 3. 批处理脚本 (Windows)

双击运行 `run_news_crawler.bat` 获得交互式菜单界面。

## 配置文件

配置文件 `news_config.json` 包含以下设置：

```json
{
  "crawl4ai": {
    "api_url": "http://localhost:11235/crawl",
    "timeout": 120
  },
  "output": {
    "directory": "E:\\mcp-test\\obsidian wang\\newspaper",
    "encoding": "utf-8"
  },
  "schedule": {
    "daily_time": "08:00"
  },
  "philippines_news_sites": [
    {
      "name": "菲律宾每日询问者",
      "url": "https://www.inquirer.net/",
      "enabled": true
    }
  ]
}
```

## 输出格式

爬取的新闻将保存为Markdown文件，包含：

- YAML前置元数据（标题、来源、日期等）
- 新闻内容（Markdown格式）
- 相关链接列表
- 统计信息

文件命名格式：`YYYY-MM-DD_HH-MM_网站名.md`

## 故障排除

### crawl4ai服务问题

1. 检查服务状态：
```bash
docker ps | findstr crawl4ai
```

2. 启动服务：
```bash
docker start crawl4ai
```

3. 查看服务日志：
```bash
docker logs crawl4ai
```

### 常见问题

1. **连接超时**: 检查网络连接和crawl4ai服务状态
2. **权限错误**: 确保输出目录有写入权限
3. **编码问题**: 确保系统支持UTF-8编码

## 日志文件

程序运行日志保存在 `philippines_news_crawler.log` 文件中，包含：
- 爬取开始/结束时间
- 成功/失败统计
- 错误信息和调试信息

## 定时任务设置

### Windows任务计划程序
1. 打开"任务计划程序"
2. 创建基本任务
3. 设置每天早上8点运行
4. 操作：启动程序，选择Python脚本

### Linux Cron
```bash
# 每天早上8点运行
0 8 * * * /usr/bin/python3 /path/to/enhanced_news_crawler.py --mode once
```

## 许可证

本项目仅供学习和个人使用。请遵守相关网站的robots.txt和使用条款。
