# 菲律宾新闻爬虫使用指南

## 🎉 恭喜！您的菲律宾新闻爬虫已经成功部署并测试完成！

### 📁 文件说明

1. **enhanced_news_crawler.py** - 增强版爬虫（推荐使用）
2. **philippines_news_crawler.py** - 基础版爬虫
3. **news_config.json** - 配置文件
4. **run_news_crawler.bat** - Windows批处理脚本（图形界面）
5. **README.md** - 详细文档

### 🚀 快速开始

#### 方法1：使用批处理脚本（最简单）
双击运行 `run_news_crawler.bat`，选择相应的选项即可。

#### 方法2：命令行使用

```bash
# 立即爬取一次所有新闻网站
python enhanced_news_crawler.py

# 爬取指定网站
python enhanced_news_crawler.py --mode url --url "https://www.inquirer.net/"

# 启动定时任务（每天早上8点自动运行）
python enhanced_news_crawler.py --mode schedule

# 守护进程模式（后台运行）
python enhanced_news_crawler.py --mode schedule --daemon
```

### 📊 测试结果

✅ **测试成功！** 已成功爬取以下7个菲律宾新闻网站：

1. **菲律宾每日询问者** (Inquirer.net) - 128,300字符
2. **菲律宾星报** (PhilStar.com) - 60,389字符  
3. **GMA新闻** (GMANetwork.com) - 302,971字符
4. **ABS-CBN新闻** (ABS-CBN.com) - 143,926字符
5. **Rappler** (Rappler.com) - 282,503字符
6. **Manila Bulletin** (MB.com.ph) - 113,236字符
7. **BusinessWorld** (BWWorldOnline.com) - 188,884字符

### 📂 输出文件

所有爬取的新闻都保存在：`E:\mcp-test\obsidian wang\newspaper\`

文件格式：`YYYY-MM-DD_HH-MM_网站名.md`

每个文件包含：
- YAML元数据（标题、来源、日期等）
- 新闻内容（Markdown格式）
- 相关链接
- 统计信息

### ⏰ 定时任务设置

#### 自动定时（推荐）
```bash
python enhanced_news_crawler.py --mode schedule
```

#### Windows任务计划程序
1. 打开"任务计划程序"
2. 创建基本任务
3. 设置每天早上8点运行
4. 操作：启动程序 `python enhanced_news_crawler.py --mode once`

### ⚙️ 配置文件

编辑 `news_config.json` 可以：
- 修改爬取时间
- 添加/删除新闻网站
- 调整爬取参数
- 更改输出目录

### 🔧 故障排除

#### crawl4ai服务问题
```bash
# 检查服务状态
docker ps | findstr crawl4ai

# 启动服务
docker start crawl4ai

# 查看日志
docker logs crawl4ai
```

#### 常见问题
- **连接超时**: 检查网络和crawl4ai服务
- **权限错误**: 确保输出目录有写入权限
- **编码问题**: 确保系统支持UTF-8

### 📝 日志文件

程序运行日志保存在：`philippines_news_crawler.log`

### 🎯 使用建议

1. **首次使用**: 先运行一次测试 `python enhanced_news_crawler.py`
2. **定期爬取**: 设置定时任务每天自动运行
3. **特定需求**: 使用URL模式爬取特定网站
4. **监控**: 定期检查日志文件和输出目录

### 📞 技术支持

如有问题，请检查：
1. crawl4ai服务是否正常运行
2. 网络连接是否正常
3. 输出目录权限是否正确
4. 日志文件中的错误信息

---

**🎊 享受您的菲律宾新闻爬虫吧！每天早上8点，您将自动获得最新的菲律宾新闻内容！**
