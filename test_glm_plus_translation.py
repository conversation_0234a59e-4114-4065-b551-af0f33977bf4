#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试GLM-4-Plus翻译功能
"""

import requests

def test_glm_plus_translation():
    """测试GLM-4-Plus翻译"""
    
    # 测试文本
    test_text = "Maj. Gen. <PERSON>, the man who arrested former president <PERSON> and flushed out pastor <PERSON> from his hideout in Davao City, has made history as the first graduate of the Philippine National Police Academy to be appointed chief of the 235,000-strong national police force."
    
    print("🧪 测试GLM-4-Plus翻译功能...")
    print(f"原文: {test_text}")
    print()
    
    try:
        headers = {
            "Authorization": "Bearer 7810b54663644292bcfd221e9ecf7f0dd.aPV0rs8StKq1FmD0",
            "Content-Type": "application/json"
        }
        
        # 使用GLM-4-Plus模型
        payload = {
            "model": "GLM-4-Plus",
            "messages": [
                {
                    "role": "system",
                    "content": "你是一个专业的新闻翻译专家，擅长将英文新闻准确翻译成中文。请保持新闻的专业性、准确性和可读性。"
                },
                {
                    "role": "user", 
                    "content": f"请将以下英文新闻摘要翻译成中文，要求：\n1. 保持新闻的准确性和专业性\n2. 人名、地名、机构名等专有名词要准确翻译\n3. 只返回翻译结果，不要添加任何解释\n\n英文原文：\n{test_text}"
                }
            ],
            "temperature": 0.2,
            "max_tokens": 1500
        }
        
        print("🔄 发送翻译请求...")
        
        response = requests.post(
            "https://open.bigmodel.cn/api/paas/v4/chat/completions",
            headers=headers,
            json=payload,
            timeout=45
        )
        
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            translated_text = result["choices"][0]["message"]["content"]
            
            # 清理可能的前缀
            translated_text = translated_text.strip()
            if translated_text.startswith("翻译结果：") or translated_text.startswith("中文翻译："):
                translated_text = translated_text.split("：", 1)[1].strip()
            
            print(f"✅ 翻译成功!")
            print(f"译文: {translated_text}")
        else:
            print(f"❌ 翻译API请求失败: {response.status_code}")
            print(f"响应内容: {response.text}")
            
    except Exception as e:
        print(f"❌ 翻译过程中出错: {str(e)}")

if __name__ == "__main__":
    test_glm_plus_translation()
