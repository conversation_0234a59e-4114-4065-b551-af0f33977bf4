import requests
import os
import time
import datetime
import logging
import argparse
import schedule
import threading
import json
from pathlib import Path
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("philippines_news_crawler.log"),
        logging.StreamHandler()
    ]
)

# 配置
CRAWL4AI_API = "http://localhost:11235/crawl"
OUTPUT_DIR = r"E:\mcp-test\obsidian wang\newspaper"
PHILIPPINES_NEWS_SITES = [
    "https://www.inquirer.net/",  # 菲律宾每日询问者
    "https://www.philstar.com/",  # 菲律宾星报
    "https://www.gmanetwork.com/news/",  # GMA新闻
    "https://news.abs-cbn.com/",  # ABS-CBN新闻
    "https://www.rappler.com/"    # Rappler
]

# 创建一个带有重试机制的会话
def create_session():
    session = requests.Session()
    retry = Retry(
        total=3,
        backoff_factor=0.5,
        status_forcelist=[500, 502, 503, 504]
    )
    adapter = HTTPAdapter(max_retries=retry)
    session.mount('http://', adapter)
    session.mount('https://', adapter)
    return session

def ensure_output_dir():
    """确保输出目录存在"""
    Path(OUTPUT_DIR).mkdir(parents=True, exist_ok=True)

def get_timestamp():
    """获取当前时间戳，格式为YYYY-MM-DD_HH-MM"""
    return datetime.datetime.now().strftime("%Y-%m-%d_%H-%M")

def check_crawl4ai_service():
    """检查crawl4ai服务是否可用"""
    try:
        response = requests.get("http://localhost:11235/health", timeout=5)
        if response.status_code == 200:
            return True
        else:
            logging.error(f"crawl4ai服务返回状态码: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        logging.error(f"无法连接到crawl4ai服务: {str(e)}")
        logging.info("请确保crawl4ai服务正在运行，可以通过以下命令启动: docker start crawl4ai")
        return False

def crawl_website(url, limit=20):
    """爬取指定网站的新闻"""
    logging.info(f"开始爬取: {url}")

    if not check_crawl4ai_service():
        return None

    try:
        # 准备请求参数 - 针对新闻网站优化
        payload = {
            "url": url,
            "css_selector": "article, .news-item, .story, .headline, .article-item, .post, .entry, .content-item",
            "word_count_threshold": 50,  # 过滤掉太短的内容
            "only_text": False,  # 保留链接和图片信息
            "include_links": True,
            "include_images": True,
            "wait_for": 3,  # 等待页面加载
            "timeout": 60,
            "remove_overlay": True,  # 移除弹窗
            "screenshot": False,  # 不需要截图
            "cache_mode": "bypass"  # 绕过缓存获取最新内容
        }

        # 使用带有重试机制的会话
        session = create_session()

        # 发送请求到crawl4ai API
        response = session.post(CRAWL4AI_API, json=payload, timeout=120)

        if response.status_code == 200:
            result = response.json()
            if result.get("success"):
                markdown_content = result.get("markdown", "")
                # 如果内容太少，尝试获取更多内容
                if len(markdown_content.strip()) < 500:
                    logging.warning(f"内容较少 ({len(markdown_content)} 字符)，尝试使用更宽松的选择器")
                    # 重试使用更宽松的选择器
                    payload["css_selector"] = "body"
                    payload["word_count_threshold"] = 20
                    response = session.post(CRAWL4AI_API, json=payload, timeout=120)
                    if response.status_code == 200:
                        result = response.json()
                        if result.get("success"):
                            markdown_content = result.get("markdown", "")

                return {
                    "site": url,
                    "markdown": markdown_content,
                    "title": result.get("title", "未知标题"),
                    "links": result.get("links", []),
                    "images": result.get("images", []),
                    "metadata": result.get("metadata", {})
                }
            else:
                error_msg = result.get("error_message", "未知错误")
                logging.error(f"爬取失败: {error_msg}")
                # 打印完整响应以便调试
                logging.debug(f"完整响应: {result}")
        else:
            logging.error(f"API请求失败: {response.status_code}")
            # 尝试打印响应内容以便调试
            try:
                logging.debug(f"响应内容: {response.text}")
            except:
                pass

    except Exception as e:
        logging.error(f"爬取过程中出错: {str(e)}")

    return None

def save_to_markdown(data, timestamp=None):
    """将爬取的内容保存为Markdown文件"""
    if not data or not data.get("markdown"):
        return False

    if timestamp is None:
        timestamp = get_timestamp()

    site_name = data["site"].replace("https://", "").replace("http://", "").split("/")[0]
    filename = f"{timestamp}_{site_name}.md"
    filepath = os.path.join(OUTPUT_DIR, filename)

    try:
        with open(filepath, "w", encoding="utf-8") as f:
            # 添加YAML前置元数据
            f.write("---\n")
            f.write(f"title: \"{data['title']}\"\n")
            f.write(f"source: {data['site']}\n")
            f.write(f"date: {timestamp.replace('_', ' ')}\n")
            f.write(f"crawl_time: {datetime.datetime.now().isoformat()}\n")
            f.write(f"tags: [news, philippines, {site_name}]\n")

            # 添加元数据信息
            metadata = data.get("metadata", {})
            if metadata:
                f.write(f"description: \"{metadata.get('description', '')}\"\n")
                f.write(f"keywords: \"{metadata.get('keywords', '')}\"\n")
                f.write(f"author: \"{metadata.get('author', '')}\"\n")

            # 添加统计信息
            content_length = len(data["markdown"])
            f.write(f"content_length: {content_length}\n")
            f.write(f"links_count: {len(data.get('links', []))}\n")
            f.write(f"images_count: {len(data.get('images', []))}\n")
            f.write("---\n\n")

            # 添加标题和来源
            f.write(f"# {data['title']}\n\n")
            f.write(f"**来源**: [{site_name}]({data['site']})\n")
            f.write(f"**爬取时间**: {timestamp.replace('_', ' ')}\n")
            f.write(f"**内容长度**: {content_length} 字符\n\n")

            # 如果有描述，添加描述
            if metadata.get("description"):
                f.write(f"**描述**: {metadata['description']}\n\n")

            f.write("---\n\n")

            # 添加主要内容
            f.write(data["markdown"])

            # 如果有链接，添加链接列表
            links = data.get("links", [])
            if links and len(links) > 0:
                f.write("\n\n## 相关链接\n\n")
                for i, link in enumerate(links[:10]):  # 只显示前10个链接
                    if isinstance(link, dict):
                        url = link.get("href", "")
                        text = link.get("text", url)
                    else:
                        url = str(link)
                        text = url
                    if url:
                        f.write(f"{i+1}. [{text}]({url})\n")

        logging.info(f"已保存: {filepath} (内容长度: {content_length} 字符)")
        return True
    except Exception as e:
        logging.error(f"保存文件时出错: {str(e)}")
        return False

def crawl_all_sites():
    """爬取所有配置的菲律宾新闻网站"""
    timestamp = get_timestamp()
    logging.info(f"开始每日新闻爬取任务: {timestamp}")

    ensure_output_dir()
    success_count = 0

    # 首先检查服务是否可用
    if not check_crawl4ai_service():
        logging.error("crawl4ai服务不可用，爬取任务取消")
        return

    for site in PHILIPPINES_NEWS_SITES:
        data = crawl_website(site)
        if data and save_to_markdown(data, timestamp):
            success_count += 1
        # 添加延迟以避免过快请求
        time.sleep(5)

    logging.info(f"每日爬取任务完成: 成功爬取 {success_count}/{len(PHILIPPINES_NEWS_SITES)} 个网站")

def crawl_specific_url(url):
    """爬取指定URL的内容"""
    logging.info(f"开始爬取指定URL: {url}")
    ensure_output_dir()

    # 首先检查服务是否可用
    if not check_crawl4ai_service():
        logging.error("crawl4ai服务不可用，爬取任务取消")
        return False

    data = crawl_website(url, limit=50)  # 对特定URL爬取更多内容
    if data and save_to_markdown(data):
        logging.info(f"成功爬取并保存: {url}")
        return True
    else:
        logging.error(f"爬取或保存失败: {url}")
        return False

def run_scheduler():
    """运行定时任务调度器"""
    logging.info("定时任务调度器启动，每天早上8点自动爬取新闻")

    # 设置每天早上8点运行
    schedule.every().day.at("08:00").do(crawl_all_sites)

    # 也可以设置测试用的更频繁任务（可选）
    # schedule.every(30).minutes.do(crawl_all_sites)  # 每30分钟运行一次，用于测试

    while True:
        schedule.run_pending()
        time.sleep(60)  # 每分钟检查一次

def run_scheduler_in_background():
    """在后台线程中运行调度器"""
    scheduler_thread = threading.Thread(target=run_scheduler, daemon=True)
    scheduler_thread.start()
    return scheduler_thread

def main():
    """主函数 - 支持命令行参数"""
    parser = argparse.ArgumentParser(description="菲律宾新闻爬虫")
    parser.add_argument("--mode", choices=["once", "schedule", "url"], default="once",
                       help="运行模式: once=运行一次, schedule=定时任务, url=爬取指定URL")
    parser.add_argument("--url", help="要爬取的URL (当mode=url时使用)")
    parser.add_argument("--daemon", action="store_true", help="以守护进程模式运行定时任务")

    args = parser.parse_args()

    logging.info("菲律宾新闻爬虫启动")

    if args.mode == "once":
        # 立即运行一次爬取
        logging.info("运行模式: 单次爬取")
        crawl_all_sites()

    elif args.mode == "schedule":
        if args.daemon:
            # 守护进程模式
            logging.info("运行模式: 定时任务 (守护进程)")
            try:
                run_scheduler()
            except KeyboardInterrupt:
                logging.info("收到中断信号，停止定时任务")
        else:
            # 交互模式
            logging.info("运行模式: 定时任务 (交互模式)")
            print("定时任务已启动，每天早上8点自动爬取新闻")
            print("按 Ctrl+C 停止...")

            scheduler_thread = run_scheduler_in_background()

            try:
                # 立即运行一次
                print("立即执行一次爬取任务...")
                crawl_all_sites()

                # 保持主线程运行
                while True:
                    time.sleep(1)
            except KeyboardInterrupt:
                logging.info("收到中断信号，停止定时任务")

    elif args.mode == "url":
        if not args.url:
            print("错误: 使用 --mode url 时必须指定 --url 参数")
            return

        logging.info(f"运行模式: 爬取指定URL - {args.url}")
        success = crawl_specific_url(args.url)
        if success:
            print("爬取成功！内容已保存到指定目录。")
        else:
            print("爬取失败，请查看日志文件了解详情。")

    # 提示用户其他使用方式
    if args.mode == "once":
        print("\n脚本已完成一次爬取。")
        print("\n其他使用方式:")
        print(f"  定时任务: python {os.path.basename(__file__)} --mode schedule")
        print(f"  爬取指定URL: python {os.path.basename(__file__)} --mode url --url <URL>")
        print(f"  守护进程: python {os.path.basename(__file__)} --mode schedule --daemon")
        print("\n如果爬取失败，请确保crawl4ai服务正在运行:")
        print("- 检查服务: docker ps | findstr crawl4ai")
        print("- 启动服务: docker start crawl4ai")

if __name__ == "__main__":
    main()
