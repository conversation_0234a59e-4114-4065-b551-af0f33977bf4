#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
菲律宾新闻分析器演示版
使用模拟数据演示新闻筛选、排序和翻译功能
"""

import requests
import os
import time
import datetime
import logging
import argparse
import json
from pathlib import Path

class DemoNewsAnalyzer:
    def __init__(self):
        """初始化演示分析器"""
        self.setup_logging()
        
        # 翻译API配置
        self.translation_config = {
            "api_key": "7810b54663644292bcfd221e9ecf7f0dd.aPV0rs8StKq1FmD0",
            "base_url": "https://open.bigmodel.cn/api/paas/v4",
            "model": "GLM-4-Flash"
        }
        
        # 输出目录
        self.output_dir = r"E:\mcp-test\obsidian wang\newspaper"
        Path(self.output_dir).mkdir(parents=True, exist_ok=True)
    
    def setup_logging(self):
        """设置日志"""
        logging.basicConfig(
            level=logging.INFO,
            format="%(asctime)s - %(levelname)s - %(message)s",
            handlers=[
                logging.FileHandler("demo_news_analyzer.log", encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def get_demo_news_data(self):
        """获取演示新闻数据"""
        demo_news = [
            {
                "title": "President Marcos announces new economic reforms to boost Philippine growth",
                "summary": "The President unveiled a comprehensive economic package aimed at attracting foreign investment and creating jobs.",
                "views": 15420,
                "comments": 89,
                "date": "2025-05-30",
                "category": "Politics",
                "source": "https://www.inquirer.net/",
                "link": "https://www.inquirer.net/politics/marcos-economic-reforms"
            },
            {
                "title": "Typhoon Pepito threatens Northern Luzon, thousands evacuated",
                "summary": "Weather bureau warns of dangerous winds and heavy rainfall as the typhoon approaches landfall.",
                "views": 23150,
                "comments": 156,
                "date": "2025-05-30",
                "category": "Weather",
                "source": "https://www.philstar.com/",
                "link": "https://www.philstar.com/weather/typhoon-pepito"
            },
            {
                "title": "Philippine peso strengthens against dollar amid positive economic outlook",
                "summary": "The local currency gained ground following improved investor confidence and strong remittance flows.",
                "views": 8930,
                "comments": 45,
                "date": "2025-05-30",
                "category": "Economy",
                "source": "https://www.bworldonline.com/",
                "link": "https://www.bworldonline.com/economy/peso-strengthens"
            },
            {
                "title": "Breaking: Major earthquake hits Mindanao, no tsunami threat",
                "summary": "A magnitude 6.2 earthquake struck southern Philippines but authorities say no tsunami warning issued.",
                "views": 31200,
                "comments": 203,
                "date": "2025-05-30",
                "category": "Breaking News",
                "source": "https://www.gmanetwork.com/",
                "link": "https://www.gmanetwork.com/news/earthquake-mindanao"
            },
            {
                "title": "Senate approves healthcare reform bill in landmark vote",
                "summary": "The Universal Healthcare Enhancement Act passed with bipartisan support, expanding medical coverage.",
                "views": 12750,
                "comments": 78,
                "date": "2025-05-30",
                "category": "Politics",
                "source": "https://www.rappler.com/",
                "link": "https://www.rappler.com/politics/senate-healthcare-reform"
            },
            {
                "title": "Philippine basketball team qualifies for Asian Games finals",
                "summary": "Gilas Pilipinas secured their spot in the championship game with a thrilling victory over South Korea.",
                "views": 18640,
                "comments": 234,
                "date": "2025-05-30",
                "category": "Sports",
                "source": "https://www.inquirer.net/",
                "link": "https://www.inquirer.net/sports/gilas-asian-games"
            },
            {
                "title": "COVID-19 cases decline for third consecutive week nationwide",
                "summary": "Health officials report continued improvement in pandemic indicators across all regions.",
                "views": 9870,
                "comments": 67,
                "date": "2025-05-30",
                "category": "Health",
                "source": "https://news.abs-cbn.com/",
                "link": "https://news.abs-cbn.com/health/covid-decline"
            },
            {
                "title": "Manila traffic congestion reaches critical levels during rush hour",
                "summary": "MMDA implements new traffic schemes as commuter complaints mount over extended travel times.",
                "views": 14320,
                "comments": 189,
                "date": "2025-05-30",
                "category": "Metro Manila",
                "source": "https://mb.com.ph/",
                "link": "https://mb.com.ph/metro/manila-traffic-crisis"
            },
            {
                "title": "Exclusive: Former senator arrested in corruption probe",
                "summary": "Anti-graft investigators filed charges against the ex-lawmaker in connection with infrastructure kickbacks.",
                "views": 27890,
                "comments": 312,
                "date": "2025-05-30",
                "category": "Crime",
                "source": "https://www.rappler.com/",
                "link": "https://www.rappler.com/crime/senator-arrested-corruption"
            },
            {
                "title": "Philippine startup raises $50M in Series B funding round",
                "summary": "The fintech company plans to expand operations across Southeast Asia with the new investment.",
                "views": 6540,
                "comments": 34,
                "date": "2025-05-30",
                "category": "Business",
                "source": "https://www.bworldonline.com/",
                "link": "https://www.bworldonline.com/business/startup-funding"
            }
        ]
        
        # 计算热度分数
        for news in demo_news:
            news["popularity_score"] = self.calculate_popularity_score(news)
        
        return demo_news
    
    def calculate_popularity_score(self, news):
        """计算新闻的热度分数"""
        score = 0
        
        # 基于浏览量
        views = news.get("views", 0)
        if views > 0:
            score += min(views / 1000, 50)  # 最多50分
        
        # 基于评论数
        comments = news.get("comments", 0)
        if comments > 0:
            score += min(comments * 0.5, 30)  # 最多30分
        
        # 基于标题关键词
        title = news.get("title", "").lower()
        important_keywords = {
            "breaking": 20, "exclusive": 15, "urgent": 15,
            "president": 10, "marcos": 10, "government": 8,
            "earthquake": 12, "typhoon": 12, "disaster": 10,
            "arrested": 8, "corruption": 8, "crime": 6,
            "economy": 6, "peso": 5, "investment": 5
        }
        
        for keyword, points in important_keywords.items():
            if keyword in title:
                score += points
        
        # 基于分类
        category_scores = {
            "Breaking News": 15,
            "Politics": 10,
            "Crime": 8,
            "Economy": 6,
            "Weather": 8,
            "Health": 5,
            "Sports": 4,
            "Business": 5,
            "Metro Manila": 6
        }
        
        category = news.get("category", "")
        score += category_scores.get(category, 0)
        
        return round(score, 1)
    
    def translate_text(self, text, target_language="中文"):
        """使用GLM-4-Flash翻译文本"""
        if not text or len(text.strip()) < 5:
            return text
        
        try:
            headers = {
                "Authorization": f"Bearer {self.translation_config['api_key']}",
                "Content-Type": "application/json"
            }
            
            prompt = f"请将以下英文新闻翻译成{target_language}，保持新闻的准确性和可读性：\n\n{text}"
            
            payload = {
                "model": self.translation_config["model"],
                "messages": [
                    {"role": "user", "content": prompt}
                ],
                "temperature": 0.3,
                "max_tokens": 2000
            }
            
            response = requests.post(
                f"{self.translation_config['base_url']}/chat/completions",
                headers=headers,
                json=payload,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                translated_text = result["choices"][0]["message"]["content"]
                return translated_text.strip()
            else:
                self.logger.error(f"翻译API请求失败: {response.status_code}")
                return text
                
        except Exception as e:
            self.logger.error(f"翻译过程中出错: {str(e)}")
            return text
    
    def analyze_and_generate_summary(self):
        """分析新闻并生成汇总"""
        self.logger.info("开始演示新闻分析...")
        
        # 获取演示数据
        all_news = self.get_demo_news_data()
        
        # 按热度排序并选择前10条
        sorted_news = sorted(all_news, key=lambda x: x["popularity_score"], reverse=True)
        top_news = sorted_news[:10]
        
        self.logger.info(f"选择热度最高的 {len(top_news)} 条新闻进行翻译")
        
        # 翻译新闻
        translated_news = []
        for i, news in enumerate(top_news, 1):
            self.logger.info(f"翻译第 {i} 条新闻: {news['title'][:50]}...")
            
            # 翻译标题和摘要
            news["title_zh"] = self.translate_text(news["title"])
            news["summary_zh"] = self.translate_text(news["summary"])
            
            translated_news.append(news)
            time.sleep(1)  # 避免API限制
        
        # 生成汇总文件
        self.generate_summary_file(translated_news)
        
        return translated_news
    
    def generate_summary_file(self, news_list):
        """生成汇总文件"""
        timestamp = datetime.datetime.now().strftime("%Y-%m-%d_%H-%M")
        date_str = datetime.datetime.now().strftime("%Y年%m月%d日")
        filename = f"{timestamp}_汇总.md"
        filepath = os.path.join(self.output_dir, filename)
        
        try:
            with open(filepath, "w", encoding="utf-8") as f:
                # 写入标题和元数据
                f.write("---\n")
                f.write(f"title: \"菲律宾新闻热点汇总 - {date_str}\"\n")
                f.write(f"date: {timestamp.replace('_', ' ')}\n")
                f.write(f"type: summary\n")
                f.write(f"news_count: {len(news_list)}\n")
                f.write("tags: [news, philippines, summary, translated, demo]\n")
                f.write("---\n\n")
                
                # 写入汇总标题
                f.write(f"# 菲律宾新闻热点汇总 - {date_str} (演示版)\n\n")
                f.write(f"**生成时间**: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"**新闻数量**: {len(news_list)} 条\n")
                f.write(f"**排序方式**: 按热度排序（浏览量、评论数、关键词等综合评分）\n")
                f.write(f"**数据来源**: 演示数据\n\n")
                
                f.write("---\n\n")
                
                # 写入新闻列表
                for i, news in enumerate(news_list, 1):
                    f.write(f"## {i}. {news.get('title_zh', news['title'])}\n\n")
                    
                    # 基本信息
                    f.write(f"**原标题**: {news['title']}\n")
                    f.write(f"**来源**: {news['source']}\n")
                    f.write(f"**热度评分**: {news['popularity_score']:.1f}\n")
                    f.write(f"**分类**: {news['category']}\n")
                    f.write(f"**发布时间**: {news['date']}\n")
                    f.write(f"**浏览量**: {news['views']:,}\n")
                    f.write(f"**评论数**: {news['comments']}\n")
                    
                    f.write("\n")
                    
                    # 摘要
                    f.write(f"**摘要**: {news.get('summary_zh', news['summary'])}\n\n")
                    
                    # 原文链接
                    f.write(f"**原文链接**: [{news['title']}]({news['link']})\n\n")
                    
                    f.write("---\n\n")
                
                # 写入统计信息
                f.write("## 统计信息\n\n")
                
                # 来源分布
                sources = {}
                for news in news_list:
                    source = news["source"]
                    domain = source.replace("https://", "").replace("http://", "").split("/")[0]
                    sources[domain] = sources.get(domain, 0) + 1
                
                f.write("**新闻来源分布**:\n")
                for source, count in sources.items():
                    f.write(f"- {source}: {count} 条\n")
                
                # 分类分布
                categories = {}
                for news in news_list:
                    category = news["category"]
                    categories[category] = categories.get(category, 0) + 1
                
                f.write(f"\n**新闻分类分布**:\n")
                for category, count in categories.items():
                    f.write(f"- {category}: {count} 条\n")
                
                f.write(f"\n**平均热度评分**: {sum(news['popularity_score'] for news in news_list) / len(news_list):.1f}\n")
                f.write(f"**总浏览量**: {sum(news['views'] for news in news_list):,}\n")
                f.write(f"**总评论数**: {sum(news['comments'] for news in news_list):,}\n")
                
            self.logger.info(f"汇总文件已生成: {filepath}")
            
        except Exception as e:
            self.logger.error(f"生成汇总文件时出错: {str(e)}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="菲律宾新闻分析器演示版")
    parser.add_argument("--no-translate", action="store_true", help="跳过翻译步骤")
    
    args = parser.parse_args()
    
    analyzer = DemoNewsAnalyzer()
    
    print("🚀 菲律宾新闻分析器演示版")
    print("📊 这是一个功能演示，使用模拟的新闻数据")
    print("⏱️  预计需要 2-3 分钟完成翻译和汇总\n")
    
    try:
        if args.no_translate:
            # 只显示分析结果，不翻译
            news_data = analyzer.get_demo_news_data()
            sorted_news = sorted(news_data, key=lambda x: x["popularity_score"], reverse=True)
            
            print("✅ 新闻热度分析完成！")
            print(f"📈 共分析了 {len(news_data)} 条新闻")
            print(f"\n🔥 热度最高的前5条新闻:")
            
            for i, news in enumerate(sorted_news[:5], 1):
                print(f"  {i}. {news['title'][:60]}...")
                print(f"     热度: {news['popularity_score']:.1f} | 浏览: {news['views']:,} | 评论: {news['comments']}")
                print()
        else:
            # 完整分析和翻译
            translated_news = analyzer.analyze_and_generate_summary()
            
            print(f"\n✅ 分析和翻译完成！")
            print(f"📈 共处理了 {len(translated_news)} 条热点新闻")
            print(f"📁 汇总文件已保存到: {analyzer.output_dir}")
            print(f"📝 文件名格式: YYYY-MM-DD_HH-MM_汇总.md")
            
            # 显示前3条新闻标题
            print(f"\n🔥 热度最高的前3条新闻:")
            for i, news in enumerate(translated_news[:3], 1):
                title_zh = news.get('title_zh', news['title'])
                score = news['popularity_score']
                print(f"  {i}. {title_zh} (热度: {score:.1f})")
        
    except Exception as e:
        analyzer.logger.error(f"演示过程中出错: {str(e)}")
        print(f"❌ 演示失败: {str(e)}")
    
    print(f"\n📋 使用说明:")
    print(f"  完整演示: python {os.path.basename(__file__)}")
    print(f"  跳过翻译: python {os.path.basename(__file__)} --no-translate")


if __name__ == "__main__":
    main()
