#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试GLM-4-Air翻译功能
"""

import requests

def test_glm_translation():
    """测试GLM-4-Air翻译"""
    
    # 测试文本
    test_text = "President <PERSON> announces new economic reforms to boost Philippine growth"
    
    print("🧪 测试GLM-4-Air翻译功能...")
    print(f"原文: {test_text}")
    
    try:
        headers = {
            "Authorization": "Bearer 7810b54663644292bcfd221e9ecf7f0dd.aPV0rs8StKq1FmD0",
            "Content-Type": "application/json"
        }
        
        # 使用GLM-4-Air模型
        payload = {
            "model": "GLM-4-Air",
            "messages": [
                {
                    "role": "user", 
                    "content": f"请将以下英文新闻翻译成中文，保持新闻的准确性和可读性：\n\n{test_text}"
                }
            ],
            "temperature": 0.3,
            "max_tokens": 2000
        }
        
        print("🔄 发送翻译请求...")
        
        response = requests.post(
            "https://open.bigmodel.cn/api/paas/v4/chat/completions",
            headers=headers,
            json=payload,
            timeout=30
        )
        
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            translated_text = result["choices"][0]["message"]["content"]
            print(f"✅ 翻译成功!")
            print(f"译文: {translated_text.strip()}")
        else:
            print(f"❌ 翻译API请求失败: {response.status_code}")
            print(f"响应内容: {response.text}")
            
    except Exception as e:
        print(f"❌ 翻译过程中出错: {str(e)}")

if __name__ == "__main__":
    test_glm_translation()
