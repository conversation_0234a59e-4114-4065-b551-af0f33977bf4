{"crawl4ai": {"api_url": "http://localhost:11235/crawl", "health_check_url": "http://localhost:11235/health", "timeout": 120, "retry_attempts": 3}, "output": {"directory": "E:\\mcp-test\\obsidian wang\\newspaper", "filename_format": "{timestamp}_{site_name}.md", "encoding": "utf-8"}, "schedule": {"daily_time": "08:00", "timezone": "Asia/Manila"}, "philippines_news_sites": [{"name": "菲律宾每日询问者", "url": "https://www.inquirer.net/", "css_selector": "article, .news-item, .story, .headline", "enabled": true}, {"name": "菲律宾星报", "url": "https://www.philstar.com/", "css_selector": "article, .news-item, .story, .headline", "enabled": true}, {"name": "GMA新闻", "url": "https://www.gmanetwork.com/news/", "css_selector": "article, .news-item, .story, .headline", "enabled": true}, {"name": "ABS-CBN新闻", "url": "https://news.abs-cbn.com/", "css_selector": "article, .news-item, .story, .headline", "enabled": true}, {"name": "<PERSON><PERSON>", "url": "https://www.rappler.com/", "css_selector": "article, .news-item, .story, .headline", "enabled": true}, {"name": "Manila Bulletin", "url": "https://mb.com.ph/", "css_selector": "article, .news-item, .story, .headline", "enabled": true}, {"name": "BusinessWorld", "url": "https://www.bworldonline.com/", "css_selector": "article, .news-item, .story, .headline", "enabled": true}], "crawl_settings": {"word_count_threshold": 50, "include_links": true, "include_images": true, "wait_for_seconds": 3, "remove_overlay": true, "cache_mode": "bypass", "delay_between_requests": 5}, "logging": {"level": "INFO", "file": "philippines_news_crawler.log", "format": "%(asctime)s - %(levelname)s - %(message)s"}}