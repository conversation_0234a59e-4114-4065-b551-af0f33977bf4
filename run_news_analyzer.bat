@echo off
chcp 65001 > nul
echo 菲律宾新闻智能分析器
echo ========================

:menu
echo.
echo 请选择功能:
echo 1. 📡 真实新闻分析 (RSS源+智能排序+翻译) [推荐]
echo 2. 🔥 智能分析热点新闻 (网页爬取+排序+翻译)
echo 3. 📰 基础爬取所有新闻网站
echo 4. 🧪 测试单个网站分析
echo 5. 🌐 功能演示 (使用模拟数据)
echo 6. ⚙️  检查crawl4ai服务状态
echo 7. 🚀 启动crawl4ai服务
echo 8. 📁 打开新闻文件夹
echo 9. 📋 查看使用说明
echo 0. 退出
echo.

set /p choice=请输入选择 (0-9):

if "%choice%"=="1" goto real_news_analyze
if "%choice%"=="2" goto smart_analyze
if "%choice%"=="3" goto basic_crawl
if "%choice%"=="4" goto test_site
if "%choice%"=="5" goto demo_mode
if "%choice%"=="6" goto check_service
if "%choice%"=="7" goto start_service
if "%choice%"=="8" goto open_folder
if "%choice%"=="9" goto show_help
if "%choice%"=="0" goto exit
echo 无效选择，请重新输入
goto menu

:real_news_analyze
echo 📡 开始真实新闻分析...
echo 从RSS源获取最新菲律宾新闻，智能排序并翻译
echo 这是推荐的方式，数据最真实可靠
echo.
python real_news_crawler.py
echo.
echo 真实新闻分析完成！
pause
goto menu

:smart_analyze
echo 🚀 开始智能新闻分析...
echo 这将包括：新闻提取 → 热度排序 → 翻译 → 生成汇总
echo 预计需要 5-10 分钟，请耐心等待...
echo.
python news_analyzer_crawler.py --mode analyze
echo.
echo 分析完成！汇总文件已生成。
pause
goto menu

:basic_crawl
echo 📰 开始基础新闻爬取...
python enhanced_news_crawler.py --mode once
pause
goto menu

:test_site
set /p test_url=请输入要测试的网站URL:
if "%test_url%"=="" (
    echo URL不能为空
    pause
    goto menu
)
echo 🧪 测试网站分析: %test_url%
python news_analyzer_crawler.py --mode test --url "%test_url%"
pause
goto menu

:demo_mode
echo 🌐 功能演示模式...
echo.
echo 请选择演示类型:
echo 1. 完整演示 (包含翻译)
echo 2. 快速演示 (跳过翻译)
echo 3. 返回主菜单
echo.
set /p demo_choice=请选择 (1-3):

if "%demo_choice%"=="1" (
    echo 🚀 开始完整功能演示...
    python demo_news_analyzer.py
) else if "%demo_choice%"=="2" (
    echo ⚡ 开始快速演示...
    python demo_news_analyzer.py --no-translate
) else if "%demo_choice%"=="3" (
    goto menu
) else (
    echo 无效选择
)
pause
goto menu

:check_service
echo ⚙️  检查crawl4ai服务状态...
docker ps | findstr crawl4ai
if %errorlevel%==0 (
    echo ✅ crawl4ai服务正在运行
) else (
    echo ❌ crawl4ai服务未运行
)
pause
goto menu

:start_service
echo 🚀 启动crawl4ai服务...
docker start crawl4ai
if %errorlevel%==0 (
    echo ✅ crawl4ai服务启动成功
) else (
    echo ❌ crawl4ai服务启动失败，请检查Docker是否正在运行
)
pause
goto menu

:open_folder
echo 📁 打开新闻文件夹...
start "" "E:\mcp-test\obsidian wang\newspaper"
goto menu

:show_help
echo 📋 使用说明
echo ============
echo.
echo 🔥 智能分析功能:
echo   - 自动提取各大菲律宾新闻网站的热点新闻
echo   - 根据浏览量、评论数、关键词等计算热度评分
echo   - 筛选出热度最高的10条新闻
echo   - 自动翻译成中文
echo   - 生成格式化的汇总文件
echo.
echo 📰 基础爬取功能:
echo   - 爬取所有配置的新闻网站完整内容
echo   - 保存为单独的Markdown文件
echo.
echo 🧪 测试功能:
echo   - 测试单个网站的新闻提取效果
echo   - 验证翻译功能是否正常
echo.
echo 📁 输出文件:
echo   - 智能汇总: YYYY-MM-DD_HH-MM_汇总.md
echo   - 基础爬取: YYYY-MM-DD_HH-MM_网站名.md
echo   - 保存位置: E:\mcp-test\obsidian wang\newspaper\
echo.
echo 🔧 故障排除:
echo   - 确保crawl4ai服务正在运行
echo   - 检查网络连接
echo   - 查看日志文件: philippines_news_crawler.log
echo.
pause
goto menu

:exit
echo 再见！感谢使用菲律宾新闻智能分析器！
exit /b 0
