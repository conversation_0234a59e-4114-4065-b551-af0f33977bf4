#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试翻译功能
"""

import subprocess
import tempfile
import os

def test_translate_command():
    """测试translate命令"""
    
    # 测试文本
    test_text = "President <PERSON> announces new economic reforms to boost Philippine growth"
    
    print("🧪 测试翻译功能...")
    print(f"原文: {test_text}")
    
    try:
        # 创建临时文件
        with tempfile.NamedTemporaryFile(mode='w', encoding='utf-8', delete=False, suffix='.txt') as temp_file:
            temp_file.write(test_text)
            temp_file_path = temp_file.name
        
        try:
            # 构建translate命令
            cmd = [
                'translate',
                '--openai',
                '--openai-model', 'GLM-4-Air',
                '--openai-api-key', '7810b54663644292bcfd221e9ecf7f0dd.aPV0rs8StKq1FmD0',
                '--openai-base-url', 'https://open.bigmodel.cn/api/paas/v4',
                '--to', 'zh',
                '--file', temp_file_path
            ]
            
            print("🔄 执行翻译命令...")
            
            # 执行翻译命令
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                encoding='utf-8',
                timeout=60
            )
            
            print(f"返回码: {result.returncode}")
            print(f"标准输出: {result.stdout}")
            print(f"错误输出: {result.stderr}")
            
            if result.returncode == 0:
                translated_text = result.stdout.strip()
                if translated_text:
                    print(f"✅ 翻译成功!")
                    print(f"译文: {translated_text}")
                else:
                    print("❌ 翻译命令返回空结果")
            else:
                print(f"❌ 翻译命令失败: {result.stderr}")
                
        finally:
            # 清理临时文件
            try:
                os.unlink(temp_file_path)
            except:
                pass
                
    except subprocess.TimeoutExpired:
        print("❌ 翻译命令超时")
    except Exception as e:
        print(f"❌ 翻译过程中出错: {str(e)}")

if __name__ == "__main__":
    test_translate_command()
